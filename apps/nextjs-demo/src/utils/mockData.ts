import { FileRejection, FileWithPath, FileWithStatus, UploadStatus } from '@hxnova/mi-react-components/types';

const fileContent =
  'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur nec eros sollicitudin, malesuada neque non, dapibus felis. Duis nec dolor magna. Nullam maximus rhoncus metus. Fusce ac tortor ultricies, pulvinar magna sed, fringilla lacus. Suspendisse laoreet tempus nisi, non efficitur nibh tempus ac. Morbi id magna volutpat odio vulputate laoreet.';
export const mockRejectedFiles: FileRejection[] = [
  {
    file: new File([fileContent], 'Test5.dxf', {
      type: 'text/plain',
    }),
    errors: [
      {
        code: 'file-invalid-type',
        message: 'File type must be image/jpeg,.jpg,.jpeg,image/png,.png,image/gif,.gif',
      },
    ],
  },
  {
    file: new File([fileContent], 'Test6.dxf', {
      type: 'text/plain',
    }),
    errors: [
      {
        code: 'file-invalid-type',
        message: 'File type must be image/jpeg,.jpg,.jpeg,image/png,.png,image/gif,.gif',
      },
    ],
  },
  {
    file: new File([fileContent], 'Test7.dxf', {
      type: 'text/plain',
    }),
    errors: [
      {
        code: 'file-invalid-type',
        message: 'File type must be image/jpeg,.jpg,.jpeg,image/png,.png,image/gif,.gif',
      },
    ],
  },
  {
    file: new File([fileContent], 'Test8.dxf', {
      type: 'text/plain',
    }),
    errors: [
      {
        code: 'file-invalid-type',
        message: 'File type must be image/jpeg,.jpg,.jpeg,image/png,.png,image/gif,.gif',
      },
    ],
  },
  {
    file: new File([fileContent], 'Test9.dxf', {
      type: 'text/plain',
    }),
    errors: [
      {
        code: 'file-invalid-type',
        message: 'File type must be image/jpeg,.jpg,.jpeg,image/png,.png,image/gif,.gif',
      },
    ],
  },
  {
    file: new File([fileContent], 'Test10.dxf', {
      type: 'text/plain',
    }),
    errors: [
      {
        code: 'file-invalid-type',
        message: 'File type must be image/jpeg,.jpg,.jpeg,image/png,.png,image/gif,.gif',
      },
    ],
  },
];

export const mockAcceptedFiles: FileWithPath[] = [
  new File([fileContent], 'Test1.dxf', {
    type: 'text/plain',
  }),
  new File([fileContent], 'Test2.mp4', {
    type: 'text/plain',
  }),
  new File([fileContent], 'Test1.pdf', {
    type: 'text/plain',
  }),
];

export const mockAcceptedFilesForNesting: FileWithPath[] = [
  new File([], 'Test1.txt', {
    type: 'text/plain',
  }),
  new File([], 'Test1.pdf', {
    type: 'text/plain',
  }),
  new File([], 'Test1.dxf', {
    type: 'text/plain',
  }),
];

export const mockUploadingFiles: FileWithStatus[] = [
  {
    file: new File([fileContent], 'Test3.dxf', {
      type: 'text/plain',
    }),
    status: UploadStatus.Uploading,
    progress: 60,
  },
  {
    file: new File([fileContent], 'Test4.pdf', {
      type: 'text/plain',
    }),
    status: UploadStatus.Failed,
  },
];
