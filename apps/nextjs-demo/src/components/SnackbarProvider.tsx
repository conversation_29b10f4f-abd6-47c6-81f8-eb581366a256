import { Alert } from '@hxnova/react-components/Alert';
import { Snackbar } from '@hxnova/react-components/Snackbar';
import { createContext, PropsWithChildren, ReactNode, useContext, useState } from 'react';

type TriggerSnackbarFn = (message: ReactNode) => void;

const SnackbarContext = createContext<{
  triggerSnackbar: TriggerSnackbarFn;
}>({
  triggerSnackbar: () => {},
});

export const useSnackbar = () => {
  const context = useContext(SnackbarContext);
  if (!context) throw new Error('useSnackbar must be used within a SnackbarProvider');
  return context;
};

export const SnackbarProvider = ({ children }: PropsWithChildren) => {
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState<ReactNode>('');

  const triggerSnackbar: TriggerSnackbarFn = (msg) => {
    setMessage(msg);
    setOpen(true);
  };

  const handleClose = (_event?: React.SyntheticEvent | Event, _reason?: string) => {
    setOpen(false);
  };

  return (
    <SnackbarContext.Provider value={{ triggerSnackbar }}>
      {children}

      <Snackbar open={open} onClose={handleClose}>
        <Alert
          onClose={handleClose}
          sx={{
            minWidth: 200,
          }}
        >
          {message}
        </Alert>
      </Snackbar>
    </SnackbarContext.Provider>
  );
};
