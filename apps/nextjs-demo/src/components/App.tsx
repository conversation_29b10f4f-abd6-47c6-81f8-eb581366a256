'use client';

import { CssBaseline } from '@hxnova/react-components/CssBaseline';
import en_US from '@hxnova/react-components/Locale/en_US';
import { NovaProvider } from '@hxnova/react-components/NovaProvider';
import * as React from 'react';
import { useColorScheme } from './ColorScheme/ColorSchemeProvider';
import ColorSchemeToggleButton from './ColorScheme/ColorSchemeToggleButton';
import LocaleToggleButton from './LocaleToggleButton';
import { SnackbarProvider } from './SnackbarProvider';

function App({ children }: React.PropsWithChildren) {
  const [locale, setLocale] = React.useState(en_US);
  const { colorScheme } = useColorScheme();

  return (
    <NovaProvider locale={locale}>
      <body className={`${colorScheme}`}>
        <CssBaseline />
        <SnackbarProvider>
          {children}
          <LocaleToggleButton
            onLocaleToggle={(newLocale) => {
              setLocale(newLocale);
            }}
          />
          <ColorSchemeToggleButton />
        </SnackbarProvider>
      </body>
    </NovaProvider>
  );
}

export default App;
