import { Icon } from '@hxnova/icons';
import { AppSwitcher, IAppSwitcher } from '@hxnova/mi-react-components/AppSwitcher';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Actran, HxgnSfxAssetManagement, MetrologyReporting, RomaxConcept, SimManager } from '@nexusui/branding';
import { MouseEvent, useLayoutEffect, useRef, useState } from 'react';
import Heading from '../Heading';
import { useSnackbar } from '../SnackbarProvider';

const solutions: IAppSwitcher['solutions'] = [
  {
    name: 'Metrology Reporting',
    logoSrc: <MetrologyReporting width={40} height={40} />,
    url: 'https://dev.nexus.hexagon.com/product/adams',
  },
  {
    name: 'Asset Manager',
    logoSrc: <HxgnSfxAssetManagement width={40} height={40} />,
    url: 'https://dev.nexus.hexagon.com/product/adams',
  },
  {
    name: 'Romax Concept',
    logoSrc: <RomaxConcept width={40} height={40} />,
    url: 'https://dev.nexus.hexagon.com/product/adams',
  },
  {
    name: 'Sim Manager (with extremely long name)',
    logoSrc: <SimManager width={40} height={40} />,
    url: 'https://dev.nexus.hexagon.com/product/adams',
  },
  {
    name: 'Actran (_blank)',
    logoSrc: <Actran width={40} height={40} />,
    url: 'https://dev.nexus.hexagon.com/product/adams',
  },
  {
    name: 'Hexagon (_self)',
    logoSrc:
      'https://scontent-mia5-2.xx.fbcdn.net/v/t39.30808-6/506635776_1262884085842741_588525291447871506_n.jpg?_nc_cat=100&ccb=1-7&_nc_sid=6ee11a&_nc_ohc=fr-NsgQWkdYQ7kNvwG2jMl1&_nc_oc=Adn2u8-Ud6biDeHrHECnel-50Grc2ZUb7F8fDdy1eLBHV2Hy4GAMTaU5hAL9hzletsA&_nc_zt=23&_nc_ht=scontent-mia5-2.xx&_nc_gid=LUiLTOjrwzxb0cf8Vx6OHQ&oh=00_AfP5XcXElGY-HtKzmZHB9Pl9Z6C75Bnwi_ndea2QNA16Sw&oe=685CBCE8',
    url: 'https://hexagon.com/',
    target: '_self',
  },
  {
    name: 'No Logo',
    url: 'https://hexagon.com/',
  },
];

function AppSwitcherTemplate({ solutions = [], className, style, ...restProps }: Partial<IAppSwitcher>) {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const ref = useRef<any>();
  const { triggerSnackbar } = useSnackbar();

  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose: IAppSwitcher['onClose'] = (_, closeReason) => {
    setAnchorEl(null);
    triggerSnackbar(
      <p>
        Close Reason: <strong>{closeReason}</strong>
      </p>,
    );
  };

  useLayoutEffect(() => {
    setAnchorEl(ref.current);
  }, []);

  return (
    <>
      <div ref={ref}>
        <IconButton variant="neutral" onClick={handleClick} className={className} style={style}>
          <Icon family="material" name="apps" />
        </IconButton>
      </div>
      <AppSwitcher
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorEl={anchorEl}
        solutions={solutions}
        moreProductsUrl="https://dev.nexus.hexagon.com/my-products"
        {...restProps}
      />
    </>
  );
}

export default function AppSwitcherKitchen() {
  return (
    <div
      sx={{
        display: 'flex',
        gap: 20,
      }}
    >
      <div>
        <Heading>Default</Heading>
        <div
          sx={{
            display: 'flex',
            marginBottom: 600,
          }}
        >
          <AppSwitcherTemplate
            sx={{
              marginRight: 220,
            }}
          />
          <AppSwitcherTemplate
            solutions={solutions}
            sx={{
              marginRight: 320,
            }}
          />
        </div>
      </div>

      <div>
        <Heading>Loading</Heading>
        <AppSwitcherTemplate loading solutions={solutions} />
      </div>
    </div>
  );
}
