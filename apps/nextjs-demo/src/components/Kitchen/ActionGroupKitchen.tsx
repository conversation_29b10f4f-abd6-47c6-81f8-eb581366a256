import { Icon } from '@hxnova/icons';
import { ActionGroup } from '@hxnova/mi-react-components/ActionGroup';
import { useColorScheme } from '../ColorScheme/ColorSchemeProvider';
import Heading from '../Heading';

export default function ActionGroupKitchen() {
  const { colorScheme, setColorScheme } = useColorScheme();

  const actions = [
    {
      label: 'Play',
      icon: <Icon family="material" name="play_arrow" />,
      onClick: () => {},
    },
    {
      label: 'Save',
      icon: <Icon family="material" name="save" />,
      onClick: () => {},
    },
    {
      label: 'Take Photo',
      icon: <Icon family="material" name="camera_alt" />,
      onClick: () => {},
    },
    {
      label: 'Download',
      icon: <Icon family="material" name="download" />,
      onClick: () => {},
    },
    {
      label: 'View Comments',
      icon: <Icon family="material" name="chat" />,
      onClick: () => {},
    },
    {
      label: 'Toggle Color Scheme',
      icon: <Icon family="material" name="light_mode" />,
      inactiveIcon: <Icon family="material" name="dark_mode" />,
      active: colorScheme === 'dark',
      onClick: () => {
        setColorScheme(colorScheme === 'dark' ? 'light' : 'dark');
      },
    },
  ];

  return (
    <div>
      <Heading>max = 3</Heading>
      <ActionGroup
        max={3}
        actions={actions}
        sx={{
          marginBottom: 24,
        }}
      />

      <Heading>max = 6</Heading>
      <ActionGroup max={6} actions={actions} />
    </div>
  );
}
