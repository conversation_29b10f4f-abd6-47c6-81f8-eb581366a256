import { EmptyCard, IEmptyCard } from '@hxnova/mi-react-components/EmptyCard';
import { styled } from '@pigment-css/react';
import { Fragment } from 'react/jsx-runtime';
import Heading from '../Heading';
import { useSnackbar } from '../SnackbarProvider';

const StyledEmptyCard = styled(EmptyCard)<IEmptyCard>(() => ({
  variants: [
    {
      props: { mode: 'square' },
      style: {
        width: 330,
        minHeight: 280,
      },
    },
    {
      props: { mode: 'rectangle' },
      style: {
        width: 446,
      },
    },
  ],
}));

function EmptyCardTemplate(props: IEmptyCard) {
  const { triggerSnackbar } = useSnackbar();

  return (
    <StyledEmptyCard
      header="Create Project"
      description="Create a new project to get started"
      onClick={() =>
        triggerSnackbar(
          <p>
            Clicked on <strong>{props.mode}</strong> EmptyCard
          </p>,
        )
      }
      {...props}
    />
  );
}

export default function EmptyCardKitchen() {
  return (
    <div>
      {(['square', 'rectangle'] as const).map((mode) => (
        <Fragment key={mode}>
          <Heading key={mode} sx={{ marginTop: 32 }}>
            mode = {mode}
          </Heading>
          <div
            sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: 24,
              marginBottom: 32,
            }}
          >
            <EmptyCardTemplate mode={mode} />
            <EmptyCardTemplate mode={mode} loading />
            <EmptyCardTemplate mode={mode} loading loadingMode="skeleton" />
          </div>
        </Fragment>
      ))}
    </div>
  );
}
