import { Icon } from '@hxnova/icons';
import { IProductCard, ProductCard } from '@hxnova/mi-react-components/ProductCard';
import { Button } from '@hxnova/react-components/Button';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Tag } from '@hxnova/react-components/Tag';
import { Typography } from '@hxnova/react-components/Typography';
import MetrologyReportingIcon from '@nexusui/branding/MetrologyReporting';
import Heading from '../Heading';
import { useSnackbar } from '../SnackbarProvider';

const NexusLogo = (props: any) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={64} height={16} fill="none" {...props}>
    <path
      fill="#0D0D0D"
      d="M35.777 3.693c.207.21.349.48.407.775.058.294.03.599-.082.876-.11.278-.3.515-.542.682a1.443 1.443 0 0 1-1.638 0 1.505 1.505 0 0 1-.542-.682 1.553 1.553 0 0 1-.081-.876c.058-.294.2-.564.407-.775a1.45 1.45 0 0 1 1.035-.438c.388 0 .76.157 1.036.438ZM29.464 3.693c.207.21.349.48.407.775.057.294.03.599-.082.876-.11.278-.3.515-.542.682a1.443 1.443 0 0 1-1.638 0 1.506 1.506 0 0 1-.542-.682 1.554 1.554 0 0 1-.081-.876c.058-.294.2-.564.407-.775.275-.28.648-.438 1.035-.438.388 0 .76.157 1.036.438ZM26.308.438c.207.211.349.48.407.775.058.294.03.599-.081.876-.111.278-.3.515-.542.682a1.443 1.443 0 0 1-1.639 0 1.505 1.505 0 0 1-.541-.682 1.554 1.554 0 0 1-.082-.876c.058-.294.2-.564.407-.775.276-.28.648-.438 1.036-.438.387 0 .76.157 1.035.438Z"
    />
    <path
      fill="#90CA59"
      d="M32.621 6.94c.207.212.348.482.406.775.058.294.03.6-.082.877-.11.277-.3.514-.541.68a1.442 1.442 0 0 1-1.638 0 1.506 1.506 0 0 1-.542-.68 1.554 1.554 0 0 1-.082-.877c.058-.293.2-.563.406-.774.276-.282.649-.44 1.037-.44.388 0 .76.158 1.036.44Z"
    />
    <path
      fill="#0D0D0D"
      d="M38.932.438c.207.211.35.48.407.775.058.294.03.599-.081.876-.111.278-.3.515-.542.682a1.443 1.443 0 0 1-1.638 0 1.505 1.505 0 0 1-.542-.682 1.554 1.554 0 0 1-.081-.876c.058-.294.2-.564.407-.775A1.45 1.45 0 0 1 37.896 0c.388 0 .76.157 1.035.438ZM27.393 12.315a1.528 1.528 0 0 1-.407-.774c-.058-.295-.03-.6.081-.877s.3-.515.542-.682a1.443 1.443 0 0 1 1.638 0c.243.167.431.405.542.682.111.277.14.582.081.877-.058.294-.2.563-.406.774-.276.28-.648.438-1.036.438-.387 0-.76-.157-1.035-.438ZM33.706 12.315a1.527 1.527 0 0 1-.407-.774c-.058-.295-.03-.6.081-.877s.3-.515.542-.682a1.443 1.443 0 0 1 1.638 0c.243.167.431.405.542.682.111.277.14.582.082.877-.058.294-.2.563-.407.774a1.45 1.45 0 0 1-1.036.438 1.45 1.45 0 0 1-1.035-.438ZM36.861 15.562a1.527 1.527 0 0 1-.406-.775c-.058-.294-.03-.599.08-.876.112-.278.3-.515.543-.682a1.443 1.443 0 0 1 1.638 0c.242.167.43.404.542.682.11.277.14.582.081.876-.058.294-.2.564-.407.775a1.45 1.45 0 0 1-1.035.438 1.45 1.45 0 0 1-1.035-.438ZM24.237 15.562a1.527 1.527 0 0 1-.407-.775c-.058-.294-.03-.599.082-.876.11-.278.3-.515.541-.682a1.443 1.443 0 0 1 1.639 0c.242.167.43.404.542.682.11.277.139.582.08.876-.057.294-.199.564-.406.775-.276.28-.648.438-1.035.438-.388 0-.76-.157-1.036-.438ZM0 2.192h1.657l.155 1.795C2.394 2.6 4.028 1.87 5.304 1.87c2.573 0 3.994 1.59 3.994 4.281v7.666H7.406V6.428c0-1.704-.76-2.785-2.438-2.785-1.892 0-3.044 1.704-3.044 3.866v6.308H0V2.192ZM12.969 12.39c-.963-1.127-1.434-2.67-1.434-4.325 0-1.408.238-2.653.896-3.734a5.057 5.057 0 0 1 1.863-1.83 4.883 4.883 0 0 1 2.501-.631c3.088 0 4.97 2.532 4.97 5.622.008.392-.008.784-.046 1.174h-8.2c0 2.162 1.277 3.752 3.425 3.752 1.297 0 2.35-.668 2.733-2.05h1.892c-.298 2.205-2.333 3.771-4.64 3.771a5.053 5.053 0 0 1-2.165-.43 5.184 5.184 0 0 1-1.795-1.32Zm6.847-5.406c0-2.002-1.074-3.46-3.065-3.46-2.081 0-3.133 1.75-3.179 3.46h6.244ZM42.019 9.857V2.192h1.892V9.58c0 1.73.782 2.788 2.438 2.788 1.947 0 2.933-1.613 2.933-3.914v-6.27h1.902v11.633h-1.656l-.124-1.888c-.469 1.427-1.991 2.21-3.334 2.21-2.462 0-4.051-1.635-4.051-4.282ZM53.442 10.134h1.892c.044 1.565 1.276 2.378 2.954 2.378 1.366 0 2.642-.62 2.642-1.933 0-.865-.63-1.427-1.79-1.61l-2.509-.416c-1.654-.274-2.796-1.334-2.796-3.107 0-2.117 2.014-3.568 4.274-3.568 2.506 0 4.415 1.473 4.415 3.706H60.62c-.044-1.312-1.074-2.071-2.506-2.071-1.387 0-2.461.69-2.461 1.842 0 .865.63 1.298 1.545 1.449l2.663.432c1.61.275 2.931 1.45 2.931 3.198 0 2.396-2.037 3.707-4.587 3.707-2.683-.002-4.764-1.544-4.764-4.007ZM62.528 1.409c0-.2.078-.393.216-.535a.726.726 0 0 1 .52-.222c.195 0 .383.08.52.222a.768.768 0 0 1 0 1.07.726.726 0 0 1-.52.222.726.726 0 0 1-.52-.222.768.768 0 0 1-.216-.535Zm1.329 0a.63.63 0 0 0-.161-.458.598.598 0 0 0-.435-.193.583.583 0 0 0-.434.193.618.618 0 0 0-.162.458.63.63 0 0 0 .162.458.599.599 0 0 0 .434.193.583.583 0 0 0 .435-.193.618.618 0 0 0 .161-.458Zm-.956 0A.394.394 0 0 1 63 1.115a.372.372 0 0 1 .279-.121.315.315 0 0 1 .223.087c.***************.103.222h-.156a.177.177 0 0 0-.05-.122.168.168 0 0 0-.12-.051c-.139 0-.21.125-.21.277 0 .151.08.279.21.279a.166.166 0 0 0 .124-.045.175.175 0 0 0 .057-.122h.155a.334.334 0 0 1-.11.22.316.316 0 0 1-.226.08A.363.363 0 0 1 63 1.7a.386.386 0 0 1-.1-.292v.002Z"
    />
  </svg>
);

function ProductCardTemplate(props: Partial<IProductCard>) {
  const { triggerSnackbar } = useSnackbar();

  const menuActions: IProductCard['menuActions'] = [
    {
      children: 'Delete',
      onClick: () => {
        triggerSnackbar('Delete');
      },
    },
    {
      children: 'Settings',
      onClick: () => {
        triggerSnackbar('Settings');
      },
    },
  ];

  return (
    <ProductCard
      icon={<MetrologyReportingIcon />}
      headline="Metrology Reporting"
      subheading="v1.0.0"
      menuActions={menuActions}
      sx={{
        width: 350,
      }}
      {...props}
    />
  );
}

export default function ProductCardKitchen() {
  const { triggerSnackbar } = useSnackbar();

  const content: IProductCard['content'] = (
    <div
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 12,
      }}
    >
      <div
        sx={{
          padding: '8px 10px',
          backgroundColor: '#f3f3f3',
          borderRadius: 12,
          display: 'flex',
          alignItems: 'center',
        }}
      >
        <NexusLogo />
      </div>
      <Tag intensity="subtle" label="Partner Product" variant="info" />

      <Icon
        family="material"
        name="info"
        size={24}
        filled
        style={{
          color: 'var(--palette-onSurfaceVariant)',
        }}
      />
    </div>
  );

  const listActions: IProductCard['listActions'] = [
    {
      children: (
        <>
          <div
            sx={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              gap: 8,
            }}
          >
            <Tag intensity="subtle" label="Update Available" variant="warning" />
            <Typography variant="labelSmall">v.10.24</Typography>
          </div>
          <ListItemDecorator>
            <Button variant="text" size="small" endIcon={<Icon family="material" name="download" />}>
              Install
            </Button>
          </ListItemDecorator>
        </>
      ),
    },
    {
      text: 'Launch',
      icon: <Icon family="material" name="arrow_outward" />,
      onClick: () => {
        triggerSnackbar('Launch');
      },
    },
    {
      text: 'Download Title 1',
      icon: <Icon family="material" name="download" />,
      onClick: () => {
        triggerSnackbar('Download Title 1');
      },
    },
    {
      text: 'Download Title 2',
      icon: <Icon family="material" name="download" />,
      onClick: () => {
        triggerSnackbar('Download Title 2');
      },
    },
  ];

  return (
    <div>
      <Heading>loading = false</Heading>
      <div
        sx={{
          display: 'flex',
          flexWrap: 'wrap',
          alignItems: 'flex-start',
          gap: 16,
        }}
      >
        <ProductCardTemplate menuActions={undefined} />
        <ProductCardTemplate />
        <ProductCardTemplate content="Metrology Reporting is a simple, intelligent, and accessible cloud-based software solution." />
        <ProductCardTemplate content={content} />
        <ProductCardTemplate content={content} listActions={listActions.slice(1, 2)} />
        <ProductCardTemplate listActions={listActions} />
        <ProductCardTemplate content={content} listActions={listActions} />
        <ProductCardTemplate
          content={content}
          listActions={[
            ...listActions,
            <p
              sx={{
                fontStyle: 'italic',
              }}
            >
              Custom List Item 😀
            </p>,
          ]}
        />
      </div>

      <Heading>loading = true</Heading>
      <div
        sx={{
          display: 'flex',
          flexWrap: 'wrap',
          alignItems: 'flex-start',
          gap: 16,
        }}
      >
        <ProductCardTemplate loading content={content} listActions={listActions} />
        <ProductCardTemplate loading loadingMode="skeleton" content={content} listActions={listActions} />
      </div>
    </div>
  );
}
