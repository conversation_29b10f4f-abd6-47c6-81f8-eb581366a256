import { Icon } from '@hxnova/icons';
import { ActionGroup } from '@hxnova/mi-react-components/ActionGroup';
import { NavBar } from '@hxnova/mi-react-components/NavBar';
import { Avatar } from '@hxnova/react-components/Avatar';
import { AvatarGroup } from '@hxnova/react-components/AvatarGroup';
import { Button } from '@hxnova/react-components/Button';
import { Divider } from '@hxnova/react-components/Divider';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Menu } from '@hxnova/react-components/Menu';
import { MenuItem } from '@hxnova/react-components/MenuItem';
import { Tooltip } from '@hxnova/react-components/Tooltip';
import { useState } from 'react';
import Heading from '../Heading';

function ContextualNavBar() {
  const [helpMenuAnchorEl, setHelpMenuAnchorEl] = useState<HTMLElement | null>(null);
  const [isNavBarFixed, setIsNavBarFixed] = useState(false);

  const openHelpMenu = Boolean(helpMenuAnchorEl);

  const handleHelpClick = (e: React.MouseEvent<HTMLElement>) => {
    setHelpMenuAnchorEl(e.currentTarget);
  };
  const handleHelpMenuClose = () => {
    setHelpMenuAnchorEl(null);
  };
  const toggleFixedNavBar = () => {
    if (!isNavBarFixed) {
      setIsNavBarFixed(true);
      document.body.style.marginTop = '56px';
    } else {
      setIsNavBarFixed(false);
      document.body.style.marginTop = '0px';
    }
  };

  const renderNavBar = (
    <NavBar
      pageTitle="Hand crank dialog"
      navIcon={
        <IconButton variant="neutral">
          <Icon family="material" name="arrow_back" />
        </IconButton>
      }
      leftActions={
        <ActionGroup
          actions={[
            {
              label: 'AM Workflow',
              icon: <Icon family="material" name="list" />,
              onClick: () => {},
            },
            {
              label: 'Take Snapshot',
              icon: <Icon family="material" name="photo_camera" />,
              onClick: () => {},
            },
            {
              label: 'Saved Snapshots',
              icon: <Icon family="material" name="photo" />,
              onClick: () => {},
            },
          ]}
        />
      }
    >
      <div
        sx={(theme) => ({
          marginLeft: theme.vars.sys.viewport.spacing.spaceBetween.horizontal.xl,
          display: 'flex',
          alignItems: 'center',
          gap: 24,
        })}
      >
        <IconButton variant="neutral">
          <Icon family="material" name="comment" />
        </IconButton>

        <AvatarGroup>
          <Avatar>AA</Avatar>
          <Avatar>AA</Avatar>
          <Avatar>AA</Avatar>
        </AvatarGroup>

        <Button variant="outlined">Share</Button>

        <>
          <div sx={{ display: 'flex', alignItems: 'center' }}>
            <Tooltip title="Help Menu">
              <IconButton onClick={handleHelpClick} variant="neutral">
                <Icon family="material" name="help" />
              </IconButton>
            </Tooltip>
          </div>
          <Menu
            anchorEl={helpMenuAnchorEl}
            open={openHelpMenu}
            onClose={handleHelpMenuClose}
            onClick={handleHelpMenuClose}
            placement="bottom-end"
          >
            <MenuItem onClick={handleHelpMenuClose}>Request Idea</MenuItem>
            <MenuItem onClick={handleHelpMenuClose}>Custom Link 2</MenuItem>
            <MenuItem onClick={handleHelpMenuClose}>Custom Link 3</MenuItem>
            <Divider />
            <MenuItem onClick={handleHelpMenuClose}>Documentation</MenuItem>
            <MenuItem onClick={handleHelpMenuClose}>Community Forums</MenuItem>
            <MenuItem onClick={handleHelpMenuClose}>Training</MenuItem>
            <Divider />
            <MenuItem onClick={handleHelpMenuClose}>Request Help</MenuItem>
          </Menu>
        </>
      </div>
    </NavBar>
  );

  return (
    <>
      <Button onClick={toggleFixedNavBar}>{isNavBarFixed ? 'Reset NavBar' : 'Fix NavBar'}</Button>

      {isNavBarFixed ? (
        <div
          sx={(theme) => ({
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            zIndex: 1201,
            borderBottom: `1px solid ${theme.vars.palette.outlineVariant}`,
          })}
        >
          {renderNavBar}
        </div>
      ) : (
        renderNavBar
      )}
    </>
  );
}

export default function NavBarKitchen() {
  return (
    <div
      sx={{
        minHeight: '200vh',
      }}
    >
      <Heading>Contextual NavBar</Heading>
      <ContextualNavBar />
    </div>
  );
}
