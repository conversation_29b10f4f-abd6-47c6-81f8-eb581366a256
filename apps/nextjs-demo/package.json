{"name": "nextjs-demo", "version": "0.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"@hxnova/icons": "1.0.0-alpha.0", "@hxnova/mi-react-components": "workspace:*", "@hxnova/react-components": "1.0.0-alpha.10", "@hxnova/themes": "1.0.0-alpha.11", "@nexusui/branding": "^2.10.0", "@pigment-css/react": "^0.0.30", "next": "14.1.3", "react": "^18.2.0", "react-dom": "^18"}, "devDependencies": {"@pigment-css/nextjs-plugin": "^0.0.30", "@types/node": "^22.10.2", "@types/react": "^18.2.67", "@types/react-dom": "^18.2.22", "typescript": "^5.4.2"}}