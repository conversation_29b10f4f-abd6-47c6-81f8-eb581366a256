import { FileRejection, FileWithPath, FileWithStatus, UploadStatus } from '@hxnova/mi-react-components/types';

const fileContent =
  'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur nec eros sollicitudin, malesuada neque non, dapibus felis. Duis nec dolor magna. Nullam maximus rhoncus metus. Fusce ac tortor ultricies, pulvinar magna sed, fringilla lacus. Suspendisse laoreet tempus nisi, non efficitur nibh tempus ac. Morbi id magna volutpat odio vulputate laoreet.';
export const mockRejectedFiles: FileRejection[] = [
  {
    file: new File([fileContent], 'AND0001910755.dxf', {
      type: 'text/plain',
    }),
    errors: [
      {
        code: 'file-invalid-type',
        message: 'File type must be image/jpeg,.jpg,.jpeg,image/png,.png,image/gif,.gif',
      },
    ],
  },
  {
    file: new File([fileContent], 'AND0001910756.dxf', {
      type: 'text/plain',
    }),
    errors: [
      {
        code: 'file-invalid-type',
        message: 'File type must be image/jpeg,.jpg,.jpeg,image/png,.png,image/gif,.gif',
      },
    ],
  },
];

export const mockAcceptedFiles: FileWithPath[] = [
  new File([fileContent], 'test1.dxf', {
    type: 'text/plain',
  }),
  new File([fileContent], 'test1.dxf', {
    type: 'text/plain',
  }),
  new File([fileContent], 'test1.dxf', {
    type: 'text/plain',
  }),
  new File([fileContent], 'AND0001910753.dxf', {
    type: 'text/plain',
  }),
  new File([fileContent], 'AND0001910753.dxf', {
    type: 'text/plain',
  }),
  new File([fileContent], 'AND0001910754.pdf', {
    type: 'text/plain',
  }),
  new File([fileContent], 'AND0001910757.dxf', {
    type: 'text/plain',
  }),
  new File([fileContent], 'AND0001910758.dxf', {
    type: 'text/plain',
  }),
  new File([fileContent], 'AND0001910757.pdf', {
    type: 'text/plain',
  }),
  new File([fileContent], 'AND0001910759.dxf', {
    type: 'text/plain',
  }),
  new File([fileContent], 'AND0001910710.dxf', {
    type: 'text/plain',
  }),
  new File([fileContent], 'AND0001910759.pdf', {
    type: 'text/plain',
  }),
];

export const mockUploadingFiles: FileWithStatus[] = [
  {
    file: new File([fileContent], 'test1.dxf', {
      type: 'text/plain',
    }),
    status: UploadStatus.Uploading,
    progress: 20,
  },
  {
    file: new File([fileContent], 'test1.dxf', {
      type: 'text/plain',
    }),
    status: UploadStatus.Uploading,
    progress: 30,
  },
  {
    file: new File([fileContent], 'AND0001910755.dxf', {
      type: 'text/plain',
    }),
    status: UploadStatus.Uploading,
    progress: 20,
  },
  {
    file: new File([fileContent], 'AND0001910753.dxf', {
      type: 'text/plain',
    }),
    status: UploadStatus.Uploading,
    progress: 60,
  },
  {
    file: new File([fileContent], 'AND0001910754.pdf', {
      type: 'text/plain',
    }),
    status: UploadStatus.Failed,
  },
];
