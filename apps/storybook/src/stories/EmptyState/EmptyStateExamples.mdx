import { Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import SizeExample from './Examples/SizeExample';
import SizeExampleSource from './Examples/SizeExample.tsx?raw';

<Meta title="@hxnova/mi-react-components/EmptyState/Examples" />

## Sizes

EmptyState comes in 2 sizes: `small` and `medium` (default). This can be set using the `size` prop.
<div className="sb-unstyled">
    <SizeExample />
</div>
<CodeExpand code={SizeExampleSource} showBorderTop style={{marginTop: 16}}/>