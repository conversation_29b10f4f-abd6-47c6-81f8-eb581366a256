import { Icon } from '@hxnova/icons';
import { EmptyState } from '@hxnova/mi-react-components/EmptyState';
import { Button } from '@hxnova/react-components/Button';
import { Divider } from '@hxnova/react-components/Divider';

export default function SizeExample() {
  return (
    <div
      sx={{
        marginBlock: 32,
      }}
    >
      <EmptyState
        size="medium"
        header="You don't have access to this content."
        description="Currently you don't have permission to view this content."
        icon={<Icon family="material" name="lock" />}
        actions={[
          <Button variant="filled" key="return">
            Return to dashboard
          </Button>,
          <Button variant="outlined" key="request">
            Request access
          </Button>,
        ]}
      />

      <Divider sx={{ maxWidth: 400, marginInline: 'auto', marginBlock: 28 }} />

      <EmptyState
        size="small"
        header="You don't have access to this content."
        description="Currently you don't have permission to view this content."
        icon={<Icon family="material" name="lock" />}
        actions={[
          <Button variant="filled" key="return">
            Return to dashboard
          </Button>,
          <Button variant="outlined" key="request">
            Request access
          </Button>,
        ]}
      />
    </div>
  );
}
