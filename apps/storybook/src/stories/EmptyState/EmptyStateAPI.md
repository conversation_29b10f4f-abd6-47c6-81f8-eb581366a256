# API Documentation

- [EmptyState](#emptystate)

# EmptyState

API reference docs for the React EmptyState component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import { EmptyState } from '@hxnova/mi-react-components/EmptyState';
// or
import { EmptyState } from '@hxnova/mi-react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **header*** | `ReactNode` | - | The header of the component. It could be a string or a custom component. |
| **actions** | `ReactElement[]` | - | Action buttons for empty state component. |
| **description** | `ReactNode` | - | The description for additional information. It could be a string or a custom component. |
| **icon** | `ReactNode` | - | The Icon element displayed on the top of component. This could be an Icon or an image. |
| **size** | `'small' ⏐ 'medium'` | `'medium'` | The size of the component.<br>- `small` is equivalent to the dense styling with smaller text size. It is used for smaller component, such as Card.<br>- `medium` is used for larger screen, such as Table, Error page. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .MIxNovaEmptyState-root | `root` | Class name applied to the root element. |
| .MIxNovaEmptyState-icon | `icon` | Class name applied to the icon element. |
| .MIxNovaEmptyState-header | `header` | Class name applied to the header element. |
| .MIxNovaEmptyState-description | `description` | Class name applied to the description element. |

