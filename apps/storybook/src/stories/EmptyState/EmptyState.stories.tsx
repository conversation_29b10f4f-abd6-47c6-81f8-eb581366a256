import { Icon } from '@hxnova/icons';
import { EmptyState, IEmptyState } from '@hxnova/mi-react-components/EmptyState';
import { Button } from '@hxnova/react-components/Button';
import { Meta, StoryFn, StoryObj } from '@storybook/react';

const meta = {
  title: '@hxnova/mi-react-components/EmptyState',
  component: EmptyState,
  parameters: {
    design: [
      {
        name: 'Contextual',
        type: 'figma',
        url: 'https://www.figma.com/design/vYxc0XelXCJ9bgo3wNC44j/MI-x-Nova-Components?node-id=58-15608&p=f&m=dev',
      },
    ],
  },
} satisfies Meta<typeof EmptyState>;
export default meta;

type ExtraControl = {
  _showIcon?: boolean;
  _showActions?: boolean;
};

const mockActions = [
  <Button variant="filled" key="return">
    Return to dashboard
  </Button>,
  <Button variant="outlined" key="request">
    Request access
  </Button>,
];

const Template: StoryFn<IEmptyState & ExtraControl> = ({ _showIcon, _showActions, ...restProps }) => {
  return (
    <EmptyState
      {...restProps}
      icon={_showIcon ? <Icon family="material" name="lock" /> : undefined}
      actions={_showActions ? mockActions : undefined}
    />
  );
};

type Story = StoryObj<typeof Template>;

export const Forbidden: Story = {
  render: Template,
  args: {
    size: 'medium',
    header: "You don't have access to this content.",
    description: "Currently you don't have permission to view this content.",
    _showIcon: true,
    _showActions: true,
  },

  argTypes: {
    size: {
      control: 'radio',
      options: ['small', 'medium'],
    },
    header: {
      control: 'text',
    },
    description: {
      control: 'text',
    },
  },

  parameters: {
    controls: {
      include: ['size', 'header', 'description', '_showIcon', '_showActions'],
    },
  },
};
