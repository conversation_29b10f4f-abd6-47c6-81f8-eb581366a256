# API Documentation

- [AppSwitcher](#appswitcher)

# AppSwitcher

API reference docs for the React AppSwitcher component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import { AppSwitcher } from '@hxnova/mi-react-components/AppSwitcher';
// or
import { AppSwitcher } from '@hxnova/mi-react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **moreProductsUrl*** | `string` | - | The url for more products. Click to jump to more products page. |
| **solutions*** | `IAppItem[]` | - | The solution items. These will be concatenated with the 2 default app items (Home and My Projects).<br><br><code>interface IAppItem {<br>name: string;<br>logoSrc?: string ⏐ ReactElement;<br>url: string;<br>target?: '_blank' ⏐ '_self';<br>'data-testid'?: string;<br>}<br></code> |
| **homeUrl** | `string` | `https://nexus.hexagon.com/home/<USER>
| **loading** | `boolean` | `false` | If `true`, the loading circle will appear. |
| **myProjectsUrl** | `string` | `https://nexus.hexagon.com/platform/` | Override for the My Projects button link (e.g., to point to a different environment for testing). |
| **onClose** | `((event: object, reason: 'backdropClick' ⏐ 'escapeKeyDown') => void)` | - | Callback fired when the component requests to be closed.<br>The `reason` parameter can optionally be used to control the response to `onClose`.<br>The `reason` value can be `'backdropClick'` or `'escapeKeyDown'`. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

### Inheritance

The props of the Nova [Popper](https://mui.com/base-ui/react-popper/components-api/#popper) are also available in `AppSwitcher`.

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .MIxNovaAppSwitcher-root | `root` | Class name applied to the root element. |
| .MIxNovaAppSwitcher-appItem | `appItem` | Class name applied to the app item element. |
| .MIxNovaAppSwitcher-more | `more` | Class name applied to the "more" button element at the bottom of the AppSwitcher. |
| .MIxNovaAppSwitcher-loading | `loading` | Class name applied to the loading indicator container. |

