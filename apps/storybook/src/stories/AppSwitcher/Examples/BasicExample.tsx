import { Icon } from '@hxnova/icons';
import { AppSwitcher, IAppSwitcher } from '@hxnova/mi-react-components/AppSwitcher';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Actran, HxgnSfxAssetManagement, MetrologyReporting, RomaxConcept, SimManager } from '@nexusui/branding';
import { MouseEvent, useState } from 'react';

export default function BasicExample() {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose: IAppSwitcher['onClose'] = () => {
    setAnchorEl(null);
  };

  return (
    <div>
      <IconButton onClick={handleClick} variant="neutral" aria-haspopup="true">
        <Icon family="material" name="apps" />
      </IconButton>
      <AppSwitcher
        onClose={handleClose}
        open={open}
        anchorEl={anchorEl}
        moreProductsUrl="https://dev.nexus.hexagon.com/my-products"
        solutions={[
          {
            name: 'Metrology Reporting',
            logoSrc: <MetrologyReporting width={40} height={40} />,
            url: 'https://dev.nexus.hexagon.com/product/adams',
            target: '_blank',
          },
          {
            name: 'Asset Manager',
            logoSrc: <HxgnSfxAssetManagement width={40} height={40} />,
            url: 'https://dev.nexus.hexagon.com/product/adams',
          },
          {
            name: 'Romax Concept',
            logoSrc: <RomaxConcept width={40} height={40} />,
            url: 'https://dev.nexus.hexagon.com/product/adams',
          },
          {
            name: 'Sim Manager',
            logoSrc: <SimManager width={40} height={40} />,
            url: 'https://dev.nexus.hexagon.com/product/adams',
          },
          {
            name: 'Actran',
            logoSrc: <Actran width={40} height={40} />,
            url: 'https://dev.nexus.hexagon.com/product/adams',
          },
        ]}
      />
    </div>
  );
}
