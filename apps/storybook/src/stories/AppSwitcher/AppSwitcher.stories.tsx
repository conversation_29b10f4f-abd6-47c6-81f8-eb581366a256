import { Icon } from '@hxnova/icons';
import { AppSwitcher, IAppSwitcher } from '@hxnova/mi-react-components/AppSwitcher';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Actran, HxgnSfxAssetManagement, MetrologyReporting, RomaxConcept, SimManager } from '@nexusui/branding';
import { Meta, StoryFn, StoryObj } from '@storybook/react';
import { MouseEvent, useLayoutEffect, useRef, useState } from 'react';

const meta = {
  title: '@hxnova/mi-react-components/AppSwitcher',
  component: AppSwitcher,
  parameters: {
    layout: 'padded',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/vYxc0XelXCJ9bgo3wNC44j/MI-x-Nova-Components?node-id=58-15624&p=f&m=dev',
    },
  },
} satisfies Meta<typeof AppSwitcher>;
export default meta;

const Template: StoryFn<IAppSwitcher> = (props: IAppSwitcher, { viewMode }) => {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const ref = useRef<any>();

  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const open = Boolean(anchorEl);

  useLayoutEffect(() => {
    if (viewMode === 'story') {
      setAnchorEl(ref.current);
      return () => {
        setAnchorEl(null);
      };
    }
  }, [viewMode]);

  return (
    <>
      <div ref={ref}>
        <IconButton variant="neutral" aria-haspopup="true" onClick={handleClick}>
          <Icon family="material" name="apps" />
        </IconButton>
      </div>
      <AppSwitcher {...props} open={open} onClose={handleClose} anchorEl={anchorEl} />
    </>
  );
};

type Story = StoryObj<typeof Template>;

export const Basic: Story = {
  render: Template,

  args: {
    moreProductsUrl: 'https://dev.nexus.hexagon.com/my-products',
    loading: false,
    myProjectsUrl: 'https://dev.nexus.hexagon.com/platform/',
    homeUrl: 'https://dev.nexus.hexagon.com/home/',
    solutions: [
      {
        name: 'Metrology Reporting',
        logoSrc: <MetrologyReporting width={40} height={40} />,
        url: 'https://dev.nexus.hexagon.com/product/adams',
        target: '_blank',
      },
      {
        name: 'Asset Manager',
        logoSrc: <HxgnSfxAssetManagement width={40} height={40} />,
        url: 'https://dev.nexus.hexagon.com/product/adams',
      },
      {
        name: 'Romax Concept',
        logoSrc: <RomaxConcept width={40} height={40} />,
        url: 'https://dev.nexus.hexagon.com/product/adams',
      },
      {
        name: 'Sim Manager',
        logoSrc: <SimManager width={40} height={40} />,
        url: 'https://dev.nexus.hexagon.com/product/adams',
      },
      {
        name: 'Actran',
        logoSrc: <Actran width={40} height={40} />,
        url: 'https://dev.nexus.hexagon.com/product/adams',
      },
    ],
  },

  argTypes: {
    loading: {
      control: 'boolean',
    },
    myProjectsUrl: {
      control: 'text',
    },
    homeUrl: {
      control: 'text',
    },
  },

  parameters: {
    controls: { include: ['loading', 'moreProductsUrl', 'homeUrl', 'myProjectsUrl'] },
  },
};
