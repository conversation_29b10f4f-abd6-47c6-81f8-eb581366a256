import { Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import LoadingExample from './Examples/LoadingExample';
import LoadingExampleSource from './Examples/LoadingExample.tsx?raw';

<Meta title="@hxnova/mi-react-components/AppSwitcher/Examples" />

## Basic

Below is the basic example of the AppSwitcher component that opens over the anchor element (`IconButton`). The `Home` and `My Projects` items are always included by default.

<div className="sb-unstyled">
    <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Loading

The AppSwitcher supports a loading state. When `loading` is set to `true`, a circular spinner is displayed and all item interactions are disabled.

<div className="sb-unstyled">
    <LoadingExample />
</div>
<CodeExpand code={LoadingExampleSource} showBorderTop style={{marginTop: 16}}/>