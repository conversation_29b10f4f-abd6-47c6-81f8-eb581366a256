# API Documentation

- [LoadingPanel](#loadingpanel)

# LoadingPanel

API reference docs for the React LoadingPanel component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import { LoadingPanel } from '@hxnova/mi-react-components/LoadingPanel';
// or
import { LoadingPanel } from '@hxnova/mi-react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **loader** | `ReactNode` | `<CircularProgress variant="indeterminate" />` | The loader indicates the loading progress |
| **loadingMessage** | `ReactNode` | - | The message to display under the title. It could be a string or a custom component. |
| **loadingTitle** | `ReactNode` | - | The title to display under the loader. It could be a string or a custom component. |
| **open** | `boolean` | `false` | Determine whether the loading panel is visible or not |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .MIxNovaLoadingPanel-root | `root` | Class name applied to the root element. |
| .MIxNovaLoadingPanel-loadingTitle | `loadingTitle` | Class name applied to the loading title element. |
| .MIxNovaLoadingPanel-loadingMessage | `loadingMessage` | Class name applied to the loading message element. |
| .MIxNovaLoadingPanel-progress | `progress` | Class name applied to the loader progress element. |

