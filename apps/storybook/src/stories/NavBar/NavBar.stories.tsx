import { Icon } from '@hxnova/icons';
import { ActionGroup } from '@hxnova/mi-react-components/ActionGroup';
import { INavBar, NavBar } from '@hxnova/mi-react-components/NavBar';
import { Avatar } from '@hxnova/react-components/Avatar';
import { AvatarGroup } from '@hxnova/react-components/AvatarGroup';
import { Button } from '@hxnova/react-components/Button';
import { Divider } from '@hxnova/react-components/Divider';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Menu } from '@hxnova/react-components/Menu';
import { MenuItem } from '@hxnova/react-components/MenuItem';
import { Tooltip } from '@hxnova/react-components/Tooltip';
import type { Meta, StoryFn, StoryObj } from '@storybook/react';
import { useState } from 'react';

const meta = {
  title: '@hxnova/mi-react-components/NavBar',
  component: NavBar,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/vYxc0XelXCJ9bgo3wNC44j/MI-x-Nova-Components?node-id=58-15623&p=f&m=dev',
    },
    layout: 'fullscreen',
  },
} satisfies Meta<typeof NavBar>;
export default meta;

type ExtraControl = {
  _rightActions?: React.ReactNode;
  _navIconName?: string;
  _showLeftActions?: boolean;
  _showRightActions?: boolean;
  _showAudience?: boolean;
  _showShareButton?: boolean;
  _showHelpMenu?: boolean;
};

const Template: StoryFn<INavBar & ExtraControl> = ({
  leftActions,
  _rightActions,
  _navIconName,
  _showAudience,
  _showHelpMenu,
  _showLeftActions,
  _showRightActions,
  _showShareButton,
  ...restProps
}) => {
  const [helpMenuAnchorEl, setHelpMenuAnchorEl] = useState<HTMLElement | null>(null);

  const openHelpMenu = Boolean(helpMenuAnchorEl);

  const handleHelpMenuButtonClick = (e: React.MouseEvent<HTMLElement>) => {
    setHelpMenuAnchorEl(e.currentTarget);
  };
  const handleHelpMenuClose = () => {
    setHelpMenuAnchorEl(null);
  };

  const renderHelpMenu = (
    <>
      <div sx={{ display: 'flex', alignItems: 'center' }}>
        <Tooltip title="Help Menu">
          <IconButton onClick={handleHelpMenuButtonClick} variant="neutral">
            <Icon family="material" name="help" />
          </IconButton>
        </Tooltip>
      </div>
      <Menu
        anchorEl={helpMenuAnchorEl}
        open={openHelpMenu}
        onClose={handleHelpMenuClose}
        onClick={handleHelpMenuClose}
        placement="bottom-end"
      >
        <MenuItem onClick={handleHelpMenuClose}>Request Idea</MenuItem>
        <MenuItem onClick={handleHelpMenuClose}>Custom Link 2</MenuItem>
        <MenuItem onClick={handleHelpMenuClose}>Custom Link 3</MenuItem>
        <Divider />
        <MenuItem onClick={handleHelpMenuClose}>Documentation</MenuItem>
        <MenuItem onClick={handleHelpMenuClose}>Community Forums</MenuItem>
        <MenuItem onClick={handleHelpMenuClose}>Training</MenuItem>
        <Divider />
        <MenuItem onClick={handleHelpMenuClose}>Request Help</MenuItem>
      </Menu>
    </>
  );

  return (
    <NavBar
      navIcon={
        _navIconName && (
          <IconButton variant="neutral">
            <Icon family="material" name={_navIconName} />
          </IconButton>
        )
      }
      leftActions={_showLeftActions && leftActions}
      {...restProps}
    >
      <div
        sx={(theme) => ({
          marginLeft: theme.vars.sys.viewport.spacing.spaceBetween.horizontal.xl,
          display: 'flex',
          alignItems: 'center',
          gap: 24,
        })}
      >
        {_showRightActions && _rightActions}

        {_showAudience && (
          <AvatarGroup>
            <Avatar>AA</Avatar>
            <Avatar>AA</Avatar>
            <Avatar>AA</Avatar>
          </AvatarGroup>
        )}

        {_showShareButton && <Button variant="outlined">Share</Button>}

        {_showHelpMenu && renderHelpMenu}
      </div>
    </NavBar>
  );
};

type Story = StoryObj<typeof Template>;

export const Contextual: Story = {
  render: Template,

  args: {
    pageTitle: 'Hand crank dialog',
    _navIconName: 'arrow_back',
    leftActions: (
      <ActionGroup
        actions={[
          {
            label: 'AM Workflow',
            icon: <Icon family="material" name="list" />,
            onClick: () => {},
          },
          {
            label: 'Take Snapshot',
            icon: <Icon family="material" name="photo_camera" />,
            onClick: () => {},
          },
          {
            label: 'Saved Snapshots',
            icon: <Icon family="material" name="photo" />,
            onClick: () => {},
          },
        ]}
      />
    ),
    _rightActions: (
      <IconButton variant="neutral">
        <Icon family="material" name="comment" />
      </IconButton>
    ),
    _showRightActions: true,
    _showLeftActions: true,
    _showAudience: true,
    _showShareButton: true,
    _showHelpMenu: true,
  },

  argTypes: {
    pageTitle: {
      control: 'text',
    },
    _navIconName: {
      control: 'select',
      options: ['arrow_back', 'menu', 'close'],
    },
  },

  parameters: {
    controls: {
      include: [
        'pageTitle',
        '_showLeftActions',
        '_showRightActions',
        '_showAudience',
        '_showShareButton',
        '_showHelpMenu',
        '_navIconName',
      ],
    },
  },
};
