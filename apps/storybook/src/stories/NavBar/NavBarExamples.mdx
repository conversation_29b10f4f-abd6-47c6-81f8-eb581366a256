import { Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import ContextualExample from './Examples/ContextualExample';
import ContextualExampleSource from './Examples/ContextualExample.tsx?raw';

<Meta title="@hxnova/mi-react-components/NavBar/Examples" />

## Contextual NavBar

Below is an example of Contextual NavBar. It is used for pages below the top-level in a Nexus App that require specific actions relevant to its context, such as a document.

<div className="sb-unstyled">
  <ContextualExample />
</div>
<CodeExpand code={ContextualExampleSource} showBorderTop style={{marginTop: 16}} />