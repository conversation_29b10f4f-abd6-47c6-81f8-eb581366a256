# API Documentation

- [NavBar](#navbar)

# NavBar

API reference docs for the React NavBar component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import { NavBar } from '@hxnova/mi-react-components/NavBar';
// or
import { NavBar } from '@hxnova/mi-react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **appLogo** | `ReactNode` | - | Graphic to display representing the application. |
| **badge** | `ReactNode` | - | Optional status badge to display next to the title / logo. |
| **leftActions** | `ReactNode` | - | Custom content to render on the left side of the toolbar after the (optional) title. |
| **navIcon** | `ReactNode` | - | Leftmost icon in the toolbar — typically a menu, back arrow, or close icon. |
| **pageTitle** | `ReactNode` | - | Text title to display for the page. It could be a string or a custom component. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .MIxNovaNavBar-root | `root` | Class name applied to the root element. |
| .MIxNovaNavBar-navIcon | `navIcon` | Class name applied to the nav icon element. |
| .MIxNovaNavBar-appLogo | `appLogo` | Class name applied to the app logo element. |
| .MIxNovaNavBar-pageTitleWrapper | `pageTitleWrapper` | Class name applied to the page title wrapper element. |
| .MIxNovaNavBar-pageTitleString | `pageTitleString` | Class name applied to the page title element. |
| .MIxNovaNavBar-badge | `badge` | Class name applied to the badge element. |

