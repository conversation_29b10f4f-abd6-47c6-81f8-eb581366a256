import { Icon } from '@hxnova/icons';
import { ActionGroup } from '@hxnova/mi-react-components/ActionGroup';

export default function BasicExample() {
  return (
    <ActionGroup
      actions={[
        {
          label: 'Play',
          icon: <Icon family="material" name="play_arrow" />,
          onClick: () => {},
        },
        {
          label: 'Save',
          icon: <Icon family="material" name="save" />,
          onClick: () => {},
        },
        {
          label: 'Take Photo',
          icon: <Icon family="material" name="camera_alt" />,
          onClick: () => {},
        },
      ]}
    />
  );
}
