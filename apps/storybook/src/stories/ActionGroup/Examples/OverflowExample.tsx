import { Icon } from '@hxnova/icons';
import { ActionGroup } from '@hxnova/mi-react-components/ActionGroup';

export default function OverflowExample() {
  return (
    <ActionGroup
      actions={[
        {
          label: 'Play',
          icon: <Icon family="material" name="play_arrow" />,
          onClick: () => {},
        },
        {
          label: 'Save',
          icon: <Icon family="material" name="save" />,
          onClick: () => {},
        },
        {
          label: 'Take Photo',
          icon: <Icon family="material" name="camera_alt" />,
          onClick: () => {},
        },
        {
          label: 'Download',
          icon: <Icon family="material" name="download" />,
          onClick: () => {},
        },
        {
          label: 'View Comments',
          icon: <Icon family="material" name="chat" />,
          onClick: () => {},
        },
        {
          label: 'Dashboard',
          icon: <Icon family="material" name="apps" />,
          onClick: () => {},
        },
      ]}
    />
  );
}
