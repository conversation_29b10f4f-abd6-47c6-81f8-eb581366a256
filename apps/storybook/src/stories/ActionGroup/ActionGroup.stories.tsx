import { Icon } from '@hxnova/icons';
import { ActionGroup } from '@hxnova/mi-react-components/ActionGroup';
import { Meta, StoryObj } from '@storybook/react';

const meta = {
  title: '@hxnova/mi-react-components/ActionGroup',
  component: ActionGroup,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/vYxc0XelXCJ9bgo3wNC44j/MI-x-Nova-Components?node-id=58-27569&p=f&m=dev',
    },
  },
} satisfies Meta<typeof ActionGroup>;
export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {
  args: {
    actions: [
      {
        label: 'Play',
        icon: <Icon family="material" name="play_arrow" />,
        onClick: () => {},
      },
      {
        label: 'Save',
        icon: <Icon family="material" name="save" />,
        onClick: () => {},
      },
      {
        label: 'Take Photo',
        icon: <Icon family="material" name="camera_alt" />,
        onClick: () => {},
      },
      {
        label: 'Download',
        icon: <Icon family="material" name="download" />,
        onClick: () => {},
      },
      {
        label: 'View Comments',
        icon: <Icon family="material" name="chat" />,
        onClick: () => {},
      },
      {
        label: 'Dashboard',
        icon: <Icon family="material" name="apps" />,
        onClick: () => {},
      },
    ],
    max: 3,
    size: 'medium',
  },
  argTypes: {
    size: {
      control: 'radio',
      options: ['small', 'medium', 'large'],
    },
  },
  parameters: {
    controls: {
      include: ['max', 'size'],
    },
  },
};
