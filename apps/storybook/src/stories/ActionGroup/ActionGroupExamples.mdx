import { Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import OverflowExample from './Examples/OverflowExample';
import OverflowExampleSource from './Examples/OverflowExample.tsx?raw';

<Meta title="@hxnova/mi-react-components/ActionGroup/Examples" />

## Basic

Below is a basic example of an ActionGroup with a few actions.

<div className="sb-unstyled">
    <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Overflow

If the ActionGroup has more actions than the value of `max` prop (default is 3), the other hidden actions can be accessed by clicking the 'more' icon button.
<div className="sb-unstyled">
    <OverflowExample />
</div>
<CodeExpand code={OverflowExampleSource} showBorderTop style={{marginTop: 16}}/>