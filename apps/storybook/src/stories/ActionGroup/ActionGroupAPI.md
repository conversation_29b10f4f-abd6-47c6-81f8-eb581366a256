# API Documentation

- [ActionGroup](#actiongroup)

# ActionGroup

API reference docs for the React ActionGroup component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import { ActionGroup } from '@hxnova/mi-react-components/ActionGroup';
// or
import { ActionGroup } from '@hxnova/mi-react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **actions*** | `ActionItem[]` | `[]` | List of action icons to include. Each action can be configured as a toggle to switch between icons based on active/inactive state.<br><br><code>type ActionItem = {<br>label: string;<br>icon: ReactElement;<br>inactiveIcon?: ReactElement;<br>active?: boolean;<br>onClick: () => void;<br>}<br></code> |
| **max** | `number` | `3` | Maximum number of icons to display. If the number of actions exceeds this number, the overflow will be displayed in a dropdown menu. |
| **moreActionsTooltipProps** | `Partial<TooltipProps>` | `{}` | Props to pass through to the more action IconButton [Tooltip](https://zeroheight.com/9a7698df1/p/05eaf0-tooltip/b/916910) element. |
| **size** | `'small' ⏐ 'medium' ⏐ 'large'` | `'medium'` | Size for the IconButton elements. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **TooltipProps** | `Partial<TooltipProps>` | `{}` | Props to pass through to the IconButton [Tooltip](https://zeroheight.com/9a7698df1/p/05eaf0-tooltip/b/916910) element. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .MIxNovaActionGroup-root | `root` | Class name applied to the root element. |
| .MIxNovaActionGroup-iconButtonActive | `iconButtonActive` | Class name applied to the active icon button element. |
| .MIxNovaActionGroup-iconButtonInactive | `iconButtonInactive` | Class name applied to the inactive icon button element. |
| .MIxNovaActionGroup-iconTooltip | `iconTooltip` | Class name applied to the icon tooltip element. |
| .MIxNovaActionGroup-overflowIconButton | `overflowIconButton` | Class name applied to the overflow icon button element. |
| .MIxNovaActionGroup-menu | `menu` | Class name applied to the menu element. |
| .MIxNovaActionGroup-iconMenuItem | `iconMenuItem` | Class name applied to the icon menu item element. |

