import { Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import DialogExample from './Examples/DialogExample';
import DialogExampleSource from './Examples/DialogExample.tsx?raw';
import ListItemExample from './Examples/ListItemExample';
import ListItemExampleSource from './Examples/ListItemExample.tsx?raw';

<Meta title="@hxnova/mi-react-components/Files/FilePicker/Examples" />

The `FilePicker` component is used for selecting files from your local computer, either via the file browser or by drag and drop.
This is a wrapper around the [react-dropzone](https://react-dropzone.js.org) component.

`FilePicker` includes two customizable components which can be configured via props:
* `DropZoneComponent` (default: `FilePickerBlock`): the drop zone area where users can drag and drop files or click to browse.
* `DropOverlayComponent` (default: `DropOverlay`): an overlay when a file is being dragged over the drop zone.


## Basic Usage

Below is the basic usage of the `FilePicker` component that uses the default `DropZoneComponent={FilePickerBlock}` and `DropOverlayComponent={DropOverlay}`.

The example shows how to configure the `dropZoneOptions` prop to control drop zone behavior.
It simulates file uploads and supports retrying failed uploads or removing files.
Uploaded files are displayed in a `FilePanel` component.

<div className="sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{ marginTop: 16 }} />

## Dialog FilePicker

You can also use the `FilePicker` component inside a dialog. Click the button below to open the example dialog.

<div className="sb-unstyled">
  <DialogExample />
</div>
<CodeExpand code={DialogExampleSource} showBorderTop style={{ marginTop: 16 }} />

## Customizability

You can provide custom components for the drop zone and overlay by using the `DropZoneComponent` and `DropOverlayComponent` props.

* By default, `DropZoneComponent` receives a `dropZoneProps` prop, which includes the `DropzoneState` from react-dropzone and the `dropZoneOptions` prop of `FilePicker`.
You can pass additional props to the component via `DropZoneComponentProps` prop.
* By default, `DropOverlayComponent` receives a `dropZoneOptions` prop derived from the `dropZoneOptions` prop of `FilePicker`.
You can pass additional props to the component via `DropOverlayComponentProps` prop.

### ListItem FilePicker

<div className="sb-unstyled">
  <ListItemExample />
</div>
<CodeExpand code={ListItemExampleSource} showBorderTop style={{ marginTop: 16 }} />