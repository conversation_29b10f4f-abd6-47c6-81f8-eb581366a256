import { Icon } from '@hxnova/icons';
import { FilePanel } from '@hxnova/mi-react-components/FilePanel';
import { FilePicker, IFilePicker } from '@hxnova/mi-react-components/FilePicker';
import { Button } from '@hxnova/react-components/Button';
import { Dialog } from '@hxnova/react-components/Dialog';
import { List } from '@hxnova/react-components/List';
import { ListDivider } from '@hxnova/react-components/ListDivider';
import { ListItem as ListItemComponent } from '@hxnova/react-components/ListItem';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { TextField } from '@hxnova/react-components/TextField';
import { Meta, StoryFn, StoryObj } from '@storybook/react';
import { useMemo, useState } from 'react';
import { useFilePicker } from './hooks/useFilePicker';
import { useFilePickerWithUpload } from './hooks/useFilePickerWithUpload';

const meta = {
  title: '@hxnova/mi-react-components/Files/FilePicker',
  component: FilePicker,
  parameters: {
    layout: 'padded',
    design: [
      {
        name: 'Contextual',
        type: 'figma',
        url: 'https://www.figma.com/design/vYxc0XelXCJ9bgo3wNC44j/MI-x-Nova-Components?node-id=219-69096&p=f&m=dev',
      },
    ],
    controls: {
      include: ['dropZoneOptions', 'DropZoneComponentProps', 'DropOverlayComponentProps'],
    },
  },
  args: {
    dropZoneOptions: {
      accept: {
        'image/jpeg': ['.jpg', '.jpeg'],
        'image/png': ['.png'],
        'image/gif': ['.gif'],
        'application/zip': ['.zip', '.7z'],
      },
      noClick: true,
      noKeyboard: true,
      maxFiles: 100,
      maxSize: 2 * 1024 * 1024, // 2MB
      multiple: true,
    },
    DropZoneComponentProps: {
      header: 'Uploader description',
      showDescription: true,
      extraDescription: 'Generic line of free text that can be added on a product basis if required.',
      buttonLabel: 'Upload files',
      dropZoneInstruction: 'Drag & Drop files',
      acceptedFilesDisplayList: ['jpg', 'jpeg', 'png', 'gif', 'zip', '7z'],
      showMaxFiles: true,
    },
    DropOverlayComponentProps: {
      header: 'Drop files anywhere to upload.',
      showMaxFiles: true,
      showMaxSize: true,
      extraDescription: '',
    },
  },
} satisfies Meta<typeof FilePicker>;
export default meta;

const BasicTemplate: StoryFn<IFilePicker> = (props) => {
  const { FilePanelProps, handleDrop, isDropped } = useFilePickerWithUpload();

  const DropZoneComponentProps = useMemo(() => {
    return {
      ...props.DropZoneComponentProps,
      condensed: isDropped,
      FilePanelComponent: () => isDropped && <FilePanel {...FilePanelProps} />,
    };
  }, [isDropped, props.DropZoneComponentProps, FilePanelProps]);

  return <FilePicker {...props} onDropped={handleDrop} DropZoneComponentProps={DropZoneComponentProps} />;
};

const InDialogTemplate: StoryFn<IFilePicker> = (props) => {
  const [open, setOpen] = useState(true);

  const { isDropped, FilePanelProps, handleDrop } = useFilePicker();

  const DropZoneComponentProps = useMemo(() => {
    return {
      ...props.DropZoneComponentProps,
      condensed: isDropped,
      FilePanelComponent: () => isDropped && <FilePanel {...FilePanelProps} />,
    };
  }, [FilePanelProps, props.DropZoneComponentProps, isDropped]);

  return (
    <div>
      <Button onClick={() => setOpen(true)}>Open</Button>
      <Dialog.Root open={open} onClose={() => setOpen(false)} disablePortal disableScrollLock maxWidth="md">
        <Dialog.Header>Add Project</Dialog.Header>
        <Dialog.Content topDivider bottomDivider>
          <div>
            <TextField
              label="Project Name"
              placeholder="Enter a name for new project"
              fullWidth
              sx={{ marginBottom: 8 }}
            />
            <FilePicker {...props} DropZoneComponentProps={DropZoneComponentProps} onDropped={handleDrop} />
          </div>
        </Dialog.Content>
        <Dialog.Actions>
          <Button variant="text" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="filled"
            onClick={() => {
              setOpen(false);
            }}
          >
            Save
          </Button>
        </Dialog.Actions>
      </Dialog.Root>
    </div>
  );
};

const ListItemTemplate: StoryFn<IFilePicker> = (props) => {
  const { handleDrop, FilePanelProps, isDropped } = useFilePicker();

  return (
    <div>
      <FilePicker
        {...props}
        DropZoneComponent={({ dropZoneProps, buttonLabel }) => (
          <List>
            <ListItemComponent>
              <ListItemContent primary="Cases" />
              <Button variant="text" endIcon={<Icon family="material" name="add" />} onClick={dropZoneProps?.open}>
                {buttonLabel}
              </Button>
            </ListItemComponent>
            <ListDivider />
          </List>
        )}
        onDropped={handleDrop}
      />

      {isDropped && (
        <FilePanel
          sx={{
            marginTop: 24,
          }}
          {...FilePanelProps}
        />
      )}
    </div>
  );
};

export const Basic: StoryObj<typeof BasicTemplate> = {
  render: BasicTemplate,

  args: {
    DropZoneComponentProps: {
      ...meta.args.DropZoneComponentProps,
      extraDescription: 'There is 50% chance of upload failure to test retry upload functionality.',
    },
  },
};

export const InDialog: StoryObj<typeof InDialogTemplate> = {
  render: InDialogTemplate,
};

export const ListItem: StoryObj<typeof ListItemTemplate> = {
  render: ListItemTemplate,
  args: {
    DropZoneComponentProps: {
      ...meta.args.DropZoneComponentProps,
      buttonLabel: 'Add Case',
    },
    DropOverlayComponent: () => null, // Disable overlay for ListItem example
  },
};
