import { FilePanel } from '@hxnova/mi-react-components/FilePanel';
import { FilePicker, IFilePicker } from '@hxnova/mi-react-components/FilePicker';
import { FileRejection, FileWithPath, FileWithStatus, UploadStatus } from '@hxnova/mi-react-components/types';
import { useCallback, useState } from 'react';

type UpdateNameType = {
  name: string;
  updatedName: string;
};

const getFileNameAndExtension = (fileName: string): { name: string; extension: string } => {
  const lastDotIndex = fileName.lastIndexOf('.');
  if (lastDotIndex === -1) return { name: fileName, extension: '' };

  return {
    name: fileName.slice(0, lastDotIndex),
    extension: fileName.slice(lastDotIndex + 1),
  };
};

export default function BasicExample() {
  const [isDropped, setIsDropped] = useState(false);
  const [acceptedFiles, setAcceptedFiles] = useState<FileWithPath[]>([]);
  const [rejectedFiles, setRejectedFiles] = useState<FileRejection[]>([]);
  const [uploadingFiles, setUploadingFiles] = useState<FileWithStatus[]>([]);
  const [uploadedFileNames, setUploadedFileNames] = useState<UpdateNameType[]>([]);

  const uploadFile = useCallback((fileToUpload: FileWithStatus) => {
    // Simulate file upload with a timeout
    setTimeout(() => {
      const random = Math.random();
      // 50% chance of upload failed to test retry upload
      if (random < 0.5) {
        setUploadingFiles((prev) => prev.filter((fw) => fw.file !== fileToUpload.file));
        setAcceptedFiles((prev) => [...prev, fileToUpload.file]);
      } else {
        setUploadingFiles((prev) => {
          return prev.map((fw) => (fw.file === fileToUpload.file ? { ...fw, status: UploadStatus.Failed } : fw));
        });
      }
    }, 2000);
  }, []);

  const renameFileIfDuplicate = useCallback(
    (file: File): File => {
      const { name, extension } = getFileNameAndExtension(file.name);
      const duplicateCount = uploadedFileNames.filter((r) => r.name === file.name).length;

      if (duplicateCount === 0) return file;

      const extensionWithDot = extension ? `.${extension}` : '';
      const newFileName = `${name}(${duplicateCount})${extensionWithDot}`;
      return new File([file], newFileName, { type: file.type });
    },
    [uploadedFileNames],
  );

  const handleDrop: IFilePicker['onDropped'] = useCallback(
    (acceptedFiles, rejectedFiles) => {
      setIsDropped(true);
      setRejectedFiles((prev) => [...prev, ...rejectedFiles]);
      acceptedFiles.forEach((file) => {
        const renamedFile = renameFileIfDuplicate(file);
        const newUploadedFileName: UpdateNameType = { name: file.name, updatedName: renamedFile.name };
        setUploadedFileNames((prev) => [...prev, newUploadedFileName]);

        const fileToUpload = {
          file: renamedFile,
          status: UploadStatus.Uploading,
          progress: 0,
        };
        setUploadingFiles((prev) => [...prev, fileToUpload]);
        uploadFile(fileToUpload);
      });
    },
    [uploadFile, renameFileIfDuplicate],
  );

  const handleRetryUpload = useCallback(
    (file: FileWithPath) => {
      setUploadingFiles((prev) => {
        const fileIndex = prev.findIndex((fw) => fw.file === file);
        const newUploadingFiles = [...prev];
        const fileToRetry = {
          ...newUploadingFiles[fileIndex],
          status: UploadStatus.Uploading,
          progress: 0,
        };
        newUploadingFiles[fileIndex] = fileToRetry;

        uploadFile(fileToRetry);

        return newUploadingFiles;
      });
    },
    [uploadFile],
  );

  const handleRemoveFile = useCallback((file: FileWithPath) => {
    setAcceptedFiles((prev) => prev.filter((fw) => fw !== file));
    setUploadingFiles((prev) => prev.filter((fw) => fw.file !== file));
    setUploadedFileNames((prev) => prev.filter((names) => names.updatedName !== file.name));
  }, []);

  const handleCloseAlert = useCallback((rejection: FileRejection) => {
    setRejectedFiles((prev) => prev.filter((fw) => fw.file.name !== rejection.file.name));
  }, []);

  return (
    <FilePicker
      onDropped={handleDrop}
      dropZoneOptions={{
        accept: {
          'image/jpeg': ['.jpg', '.jpeg'],
          'image/png': ['.png'],
          'image/gif': ['.gif'],
          'application/zip': ['.zip', '.7z'],
        },
        noClick: true,
        noKeyboard: true,
        maxFiles: 100,
        maxSize: 2 * 1024 * 1024, // 2MB
        multiple: true,
      }}
      DropZoneComponentProps={{
        condensed: isDropped,
        header: 'Upload Files',
        showDescription: true,
        showMaxFiles: true,
        acceptedFilesDisplayList: ['jpg', 'jpeg', 'png', 'gif', 'zip', '7z'],
        FilePanelComponent: () =>
          isDropped && (
            <FilePanel
              acceptedFiles={acceptedFiles}
              rejectedFiles={rejectedFiles}
              uploadingFiles={uploadingFiles}
              onClose={handleCloseAlert}
              onRemove={handleRemoveFile}
              onRetry={handleRetryUpload}
              getErrorMessage={(rejection) =>
                `Failed to upload ${rejection.file.name} - ${rejection.errors[0].message}`
              }
            />
          ),
      }}
      DropOverlayComponentProps={{
        header: 'Drop files anywhere to upload.',
        showMaxFiles: true,
        showMaxSize: true,
      }}
    />
  );
}
