import { Icon } from '@hxnova/icons';
import { FilePanel } from '@hxnova/mi-react-components/FilePanel';
import { FilePicker, IFilePicker } from '@hxnova/mi-react-components/FilePicker';
import { FileRejection, FileWithPath } from '@hxnova/mi-react-components/types';
import { Button } from '@hxnova/react-components/Button';
import { List } from '@hxnova/react-components/List';
import { ListDivider } from '@hxnova/react-components/ListDivider';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { Typography } from '@hxnova/react-components/Typography';
import { useCallback, useState } from 'react';

type UpdateNameType = {
  name: string;
  updatedName: string;
};

const getFileNameAndExtension = (fileName: string): { name: string; extension: string } => {
  const lastDotIndex = fileName.lastIndexOf('.');
  if (lastDotIndex === -1) return { name: fileName, extension: '' };

  return {
    name: fileName.slice(0, lastDotIndex),
    extension: fileName.slice(lastDotIndex + 1),
  };
};

export default function ListItemExample() {
  const [acceptedFiles, setAcceptedFiles] = useState<FileWithPath[]>([]);
  const [rejectedFiles, setRejectedFiles] = useState<FileRejection[]>([]);
  const [uploadedFileNames, setUploadedFileNames] = useState<UpdateNameType[]>([]);

  const renameFileIfDuplicate = useCallback(
    (file: File): File => {
      const { name, extension } = getFileNameAndExtension(file.name);
      const duplicateCount = uploadedFileNames.filter((r) => r.name === file.name).length;

      if (duplicateCount === 0) return file;

      const extensionWithDot = extension ? `.${extension}` : '';
      const newFileName = `${name}(${duplicateCount})${extensionWithDot}`;
      return new File([file], newFileName, { type: file.type });
    },
    [uploadedFileNames],
  );

  const handleDrop: IFilePicker['onDropped'] = useCallback(
    (acceptedFiles, rejectedFiles) => {
      setRejectedFiles((prev) => [...prev, ...rejectedFiles]);
      acceptedFiles.forEach((file) => {
        const renamedFile = renameFileIfDuplicate(file);
        const newUploadedFileName: UpdateNameType = { name: file.name, updatedName: renamedFile.name };
        setUploadedFileNames((prev) => [...prev, newUploadedFileName]);
        setAcceptedFiles((prev) => [...prev, renamedFile]);
      });
    },
    [renameFileIfDuplicate],
  );

  const handleRemoveFile = useCallback((file: FileWithPath) => {
    setAcceptedFiles((prev) => prev.filter((fw) => fw !== file));
    setUploadedFileNames((prev) => prev.filter((names) => names.updatedName !== file.name));
  }, []);

  const handleCloseAlert = useCallback((rejection: FileRejection) => {
    setRejectedFiles((prev) => prev.filter((fw) => fw.file.name !== rejection.file.name));
  }, []);

  return (
    <div>
      <FilePicker
        onDropped={handleDrop}
        dropZoneOptions={{
          accept: {
            'image/jpeg': ['.jpg', '.jpeg'],
            'image/png': ['.png'],
            'image/gif': ['.gif'],
            'application/zip': ['.zip', '.7z'],
          },
          noClick: true,
          noKeyboard: true,
          maxFiles: 100,
          maxSize: 2 * 1024 * 1024, // 2MB
          multiple: true,
        }}
        DropZoneComponent={({ dropZoneProps, buttonLabel }) => (
          <List>
            <ListItem>
              <ListItemContent primary="Cases" />
              <Button variant="text" endIcon={<Icon family="material" name="add" />} onClick={dropZoneProps?.open}>
                {buttonLabel}
              </Button>
            </ListItem>
            <ListDivider />
          </List>
        )}
        DropZoneComponentProps={{
          buttonLabel: 'Add Cases',
        }}
        DropOverlayComponent={({ header, dropZoneOptions }) => (
          <div
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'absolute',
              inset: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.54)',
              border: '2px dashed white',
              zIndex: 1201,
              color: 'white',
            }}
          >
            <Typography>
              {header} - Max Files: {dropZoneOptions?.maxFiles}
            </Typography>
          </div>
        )}
        DropOverlayComponentProps={{
          header: 'Drop files in this ListItem',
        }}
      />

      <FilePanel
        sx={{
          marginTop: 24,
        }}
        acceptedFiles={acceptedFiles}
        rejectedFiles={rejectedFiles}
        onClose={handleCloseAlert}
        onRemove={handleRemoveFile}
        getErrorMessage={(rejection) => `Failed to upload ${rejection.file.name} - ${rejection.errors[0].message}`}
      />
    </div>
  );
}
