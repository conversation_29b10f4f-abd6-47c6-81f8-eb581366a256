import { FilePanel } from '@hxnova/mi-react-components/FilePanel';
import { FilePicker, IFilePicker } from '@hxnova/mi-react-components/FilePicker';
import { FileRejection, FileWithPath } from '@hxnova/mi-react-components/types';
import { Button } from '@hxnova/react-components/Button';
import { Dialog, dialogRootClasses } from '@hxnova/react-components/Dialog';
import { TextField } from '@hxnova/react-components/TextField';
import { useCallback, useState } from 'react';

type UpdateNameType = {
  name: string;
  updatedName: string;
};

const getFileNameAndExtension = (fileName: string): { name: string; extension: string } => {
  const lastDotIndex = fileName.lastIndexOf('.');
  if (lastDotIndex === -1) return { name: fileName, extension: '' };

  return {
    name: fileName.slice(0, lastDotIndex),
    extension: fileName.slice(lastDotIndex + 1),
  };
};

export default function DialogExample() {
  const [open, setOpen] = useState(false);
  const [isDropped, setIsDropped] = useState(false);
  const [acceptedFiles, setAcceptedFiles] = useState<FileWithPath[]>([]);
  const [rejectedFiles, setRejectedFiles] = useState<FileRejection[]>([]);
  const [uploadedFileNames, setUploadedFileNames] = useState<UpdateNameType[]>([]);

  const renameFileIfDuplicate = useCallback(
    (file: File): File => {
      const { name, extension } = getFileNameAndExtension(file.name);
      const duplicateCount = uploadedFileNames.filter((r) => r.name === file.name).length;

      if (duplicateCount === 0) return file;

      const extensionWithDot = extension ? `.${extension}` : '';
      const newFileName = `${name}(${duplicateCount})${extensionWithDot}`;
      return new File([file], newFileName, { type: file.type });
    },
    [uploadedFileNames],
  );

  const handleDrop: IFilePicker['onDropped'] = useCallback(
    (acceptedFiles, rejectedFiles) => {
      setIsDropped(true);
      setRejectedFiles((prev) => [...prev, ...rejectedFiles]);
      acceptedFiles.forEach((file) => {
        const renamedFile = renameFileIfDuplicate(file);
        const newUploadedFileName: UpdateNameType = { name: file.name, updatedName: renamedFile.name };
        setUploadedFileNames((prev) => [...prev, newUploadedFileName]);
        setAcceptedFiles((prev) => [...prev, renamedFile]);
      });
    },
    [renameFileIfDuplicate],
  );

  const handleRemoveFile = useCallback((file: FileWithPath) => {
    setAcceptedFiles((prev) => prev.filter((fw) => fw !== file));
    setUploadedFileNames((prev) => prev.filter((names) => names.updatedName !== file.name));
  }, []);

  const handleCloseAlert = useCallback((rejection: FileRejection) => {
    setRejectedFiles((prev) => prev.filter((fw) => fw.file.name !== rejection.file.name));
  }, []);

  return (
    <>
      <Button onClick={() => setOpen(true)}>Open Dialog</Button>
      <Dialog.Root
        open={open}
        onClose={() => setOpen(false)}
        sx={{
          [`& .${dialogRootClasses.paper}`]: {
            maxWidth: 600,
            width: '100%',
          },
        }}
      >
        <Dialog.Header>Add Project</Dialog.Header>
        <Dialog.Content topDivider bottomDivider>
          <TextField
            label="Project Name"
            placeholder="Enter a name for new project"
            fullWidth
            sx={{ marginBottom: 8 }}
          />
          <FilePicker
            onDropped={handleDrop}
            dropZoneOptions={{
              accept: {
                'image/jpeg': ['.jpg', '.jpeg'],
                'image/png': ['.png'],
                'image/gif': ['.gif'],
                'application/zip': ['.zip', '.7z'],
              },
              noClick: true,
              noKeyboard: true,
              maxFiles: 100,
              maxSize: 2 * 1024 * 1024, // 2MB
              multiple: true,
            }}
            DropZoneComponentProps={{
              condensed: isDropped,
              header: 'Upload Files',
              acceptedFilesDisplayList: ['jpg', 'jpeg', 'png', 'gif', 'zip', '7z'],
              FilePanelComponent: () =>
                isDropped && (
                  <FilePanel
                    acceptedFiles={acceptedFiles}
                    rejectedFiles={rejectedFiles}
                    onClose={handleCloseAlert}
                    onRemove={handleRemoveFile}
                    getErrorMessage={(rejection) =>
                      `Failed to upload ${rejection.file.name} - ${rejection.errors[0].message}`
                    }
                  />
                ),
            }}
            sx={{
              width: '100%',
              overflow: 'unset',
            }}
          />
        </Dialog.Content>
        <Dialog.Actions>
          <Button variant="text" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="filled"
            onClick={() => {
              setIsDropped(false);
              setAcceptedFiles([]);
              setRejectedFiles([]);
              setOpen(false);
            }}
          >
            Save
          </Button>
        </Dialog.Actions>
      </Dialog.Root>
    </>
  );
}
