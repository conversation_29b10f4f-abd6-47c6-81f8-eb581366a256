# API Documentation

- [FilePicker](#filepicker)
- [DropOverlay](#dropoverlay)
- [FilePickerBlock](#filepickerblock)

# FilePicker

API reference docs for the React FilePicker component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import { FilePicker } from '@hxnova/mi-react-components/FilePicker';
// or
import { FilePicker } from '@hxnova/mi-react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **dropZoneOptions*** | `DropzoneOptions` | - | Options to configure the react-dropzone root element. This may be useful to define accepted file types, maximum file number and size,...<br>Refer to the [react-dropzone documentation](https://react-dropzone.js.org/#src) for more details. |
| **onDropped*** | `(acceptedFiles: FileWithPath[], rejectedFiles: FileRejection[], event: DropEvent) => void` | - | Callback fired whenever a drop event occurs, regardless of whether the dropped files are accepted or rejected. |
| **DropOverlayComponent** | `FunctionComponent<IDropOverlay>` | `DropOverlay` | The component used for drag and drop target overlay. This component is shown when the user drags files over the drop zone.<br>By default, it uses the built-in DropOverlay component. |
| **DropOverlayComponentProps** | `IDropOverlay` | - | Props applied to the DropOverlayComponent.<br><br><code>export interface DropOverlayProps extends StackProps {<br>header?: ReactNode;<br>showMaxFiles?: boolean;<br>showMaxSize?: boolean;<br>extraDescription?: ReactNode;<br>dropZoneOptions?: DropzoneOptions;<br>}<br></code> |
| **DropZoneComponent** | `FunctionComponent<IFilePickerBlock>` | `FilePickerBlock` | The content of FilePicker component shown when no drag is active.<br>By default, it uses the built-in FilePickerBlock component. |
| **DropZoneComponentProps** | `IFilePickerBlock` | - | Props applied to the DropZoneComponent.<br><br><code>export interface IFilePickerBlock {<br>condensed?: boolean;<br>header?: ReactNode;<br>showDescription?: boolean;<br>acceptedFilesDisplayList?: string[];<br>showMaxFiles?: boolean;<br>extraDescription?: ReactNode;<br>buttonLabel?: string;<br>dragZoneInstructions?: ReactNode;<br>dropZoneProps?: DropzoneState & DropzoneOptions;<br>FilePanelComponent?: ComponentType\<FilePanelComponentProps>;<br>}<br></code> |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .MIxNovaFilePicker-root | `root` | Class name applied to the root element. |
| .MIxNovaFilePicker-input | `input` | Class name applied to the input element. |

<br><br>

# DropOverlay

> DropOverlay is an internal component of FilePicker. It is not intended for direct use in applications.

API reference docs for the React DropOverlay component. Learn about the props, CSS, and other APIs of this exported module.

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **dropZoneOptions** | `DropzoneOptions` | - | The DropZone options.<br>You can specify `maxSize` and `maxFiles` in this object to display descriptions about the allowed maximum file size and number of files.<br>Refer to the [react-dropzone documentation](https://react-dropzone.js.org/#src) for more details. |
| **extraDescription** | `ReactNode` | - | The extra description shows below the description. It could be a string or a custom component. |
| **header** | `ReactNode` | `'Drop files anywhere to upload.'` | The header of the component. It could be a string or a custom component. |
| **showMaxFiles** | `boolean` | `true` | If `true`, the maximum number of files will be shown. |
| **showMaxSize** | `boolean` | `true` | If `true`, the maximum file size will be shown. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .MIxNovaDropOverlay-root | `root` | Class name applied to the root element. |
| .MIxNovaDropOverlay-header | `header` | Class name applied to the header element. |
| .MIxNovaDropOverlay-maxFiles | `maxFiles` | Class name applied to the maximum number of files description. |
| .MIxNovaDropOverlay-maxSize | `maxSize` | Class name applied to the maximum file size description. |
| .MIxNovaDropOverlay-extraDescription | `extraDescription` | Class name applied to the extra description element. |

<br><br>

# FilePickerBlock

> FilePickerBlock is an internal component of FilePicker. It is not intended for direct use in applications.

API reference docs for the React FilePickerBlock component. Learn about the props, CSS, and other APIs of this exported module.

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **acceptedFilesDisplayList** | `string[]` | - | A user-friendly list of accepted file types to display for the user.<br>If not specified, the component will display the values specified in the accept field of the dropZoneProps.<br>@example ['jpg', 'jpeg', 'png'] |
| **buttonLabel** | `string` | `'Upload'` | The label of the upload button. |
| **condensed** | `boolean` | `false` | If `true`, the component should render the compact horizontal style. |
| **dropZoneInstruction** | `ReactNode` | `'Drag & Drop files'` | The instruction of the drop zone. It can be a string or a custom component. |
| **dropZoneProps** | `(DropzoneRef & { isFocused: boolean; isDragActive: boolean; isDragAccept: boolean; isDragReject: boolean; isFileDialogActive: boolean; acceptedFiles: FileWithPath[]; ... 4 more ...; getInputProps: <T extends DropzoneInputProps>(props?: T) => T; } & Pick<...> & { ...; }) ⏐ undefined` | - | The DropZone state and options. |
| **extraDescription** | `ReactNode` | - | The extra description shows below the description. It can be a string or a custom component. |
| **FilePanelComponent** | `FunctionComponent<{}>` | `() => null` | The file panel component to render below description section. Commonly used to display the list of uploaded files. |
| **header** | `ReactNode` | - | The header of the component. It can be a string or a custom component. |
| **showDescription** | `boolean` | `true` | If `true`, the description will be shown below the drop zone. |
| **showMaxFiles** | `boolean` | `false` | If `true`, the maximum number of allowed files will be displayed in the description. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .MIxNovaFilePickerBlock-root | `root` | Class name applied to the root element. |
| .MIxNovaFilePickerBlock-header | `header` | Class name applied to the header. |
| .MIxNovaFilePickerBlock-dropZone | `dropZone` | Class name applied to the drop zone. |
| .MIxNovaFilePickerBlock-uploadButton | `uploadButton` | Class name applied to the upload button. |
| .MIxNovaFilePickerBlock-instruction | `instruction` | Class name applied to the instruction of drop zone. |
| .MIxNovaFilePickerBlock-subInstruction | `subInstruction` | Class name applied to the sub-instruction of drop zone (the word "or"). |
| .MIxNovaFilePickerBlock-description | `description` | Class name applied to the description container. |
| .MIxNovaFilePickerBlock-acceptedFiles | `acceptedFiles` | Class name applied to the accepted file extensions description. |
| .MIxNovaFilePickerBlock-maxFiles | `maxFiles` | Class name applied to the maximum number of files description. |
| .MIxNovaFilePickerBlock-maxSize | `maxSize` | Class name applied to the maximum file size description. |
| .MIxNovaFilePickerBlock-extraDescription | `extraDescription` | Class name applied to the extra description element below the description. |

