import { IFilePanel } from '@hxnova/mi-react-components/FilePanel';
import { IFilePicker } from '@hxnova/mi-react-components/FilePicker';
import { FileRejection, FileWithPath } from '@hxnova/mi-react-components/types';
import { action } from '@storybook/addon-actions';
import { useCallback, useMemo, useState } from 'react';
import { getFileNameAndExtension, UpdateNameType } from '../../utils/files';

/**
 * Hook to manage state and logic for FilePicker component.
 * - Tracks accepted and rejected files.
 * - Defines handlers for file drop, file removal, and rejection alert closure.
 * - Renames files that have same names with uploaded ones when dropping.
 * - Packages files lists and handlers into a `FilePanelProps` for use in the FilePanel component.
 */
export const useFilePicker = () => {
  const [isDropped, setIsDropped] = useState(false);
  const [acceptedFiles, setAcceptedFiles] = useState<FileWithPath[]>([]);
  const [rejectedFiles, setRejectedFiles] = useState<FileRejection[]>([]);
  const [uploadedFileNames, setUploadedFileNames] = useState<UpdateNameType[]>([]);

  const renameFileIfDuplicate = useCallback(
    (file: File): File => {
      const { name, extension } = getFileNameAndExtension(file.name);
      const duplicateCount = uploadedFileNames.filter((r) => r.name === file.name).length;

      if (duplicateCount === 0) return file;

      const extensionWithDot = extension ? `.${extension}` : '';
      const newFileName = `${name}(${duplicateCount})${extensionWithDot}`;
      return new File([file], newFileName, { type: file.type });
    },
    [uploadedFileNames],
  );

  const handleDrop: IFilePicker['onDropped'] = useCallback(
    (acceptedFiles, rejectedFiles, e) => {
      action('onDropped')([acceptedFiles, rejectedFiles, e]);
      setIsDropped(true);
      setRejectedFiles((prev) => [...prev, ...rejectedFiles]);
      acceptedFiles.forEach((file) => {
        const renamedFile = renameFileIfDuplicate(file);
        const newUploadedFileName: UpdateNameType = { name: file.name, updatedName: renamedFile.name };
        setUploadedFileNames((prev) => [...prev, newUploadedFileName]);
        setAcceptedFiles((prev) => [...prev, renamedFile]);
      });
    },
    [renameFileIfDuplicate],
  );

  const handleRemoveFile = useCallback((file: FileWithPath) => {
    action('onRemove')(file);
    setAcceptedFiles((prev) => prev.filter((fw) => fw !== file));
    setUploadedFileNames((prev) => prev.filter((names) => names.updatedName !== file.name));
  }, []);

  const handleCloseAlert = useCallback((rejection: FileRejection) => {
    action('onClose')(rejection);
    setRejectedFiles((prev) => prev.filter((fw) => fw.file.name !== rejection.file.name));
  }, []);

  const FilePanelProps: IFilePanel = useMemo(
    () => ({
      acceptedFiles,
      rejectedFiles,
      onRemove: handleRemoveFile,
      onClose: handleCloseAlert,
      getErrorMessage: (rejection) => `Failed to upload ${rejection.file.name} - ${rejection.errors[0].message}`,
    }),
    [acceptedFiles, rejectedFiles, handleRemoveFile, handleCloseAlert],
  );

  return {
    FilePanelProps,
    handleDrop,
    isDropped,
  };
};
