# API Documentation

- [FilePanel](#filepanel)

# FilePanel

API reference docs for the React FilePanel component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import { FilePanel } from '@hxnova/mi-react-components/FilePanel';
// or
import { FilePanel } from '@hxnova/mi-react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **acceptedFiles*** | `FileWithPath[]` | - | Accepted files that selected from your local computer. |
| **onClose*** | `(file: FileRejection) => void` | - | Callback fired when a rejected error alert is closed. |
| **onRemove*** | `(file: FileWithPath) => void` | - | Callback fired when the delete button is clicked. |
| **rejectedFiles*** | `FileRejection[]` | - | Rejected files that selected from your local computer.<br><br><code>export interface FileRejection {<br>file: FileWithPath;<br>errors: FileError[];<br>}<br></code> |
| **getErrorMessage** | `((rejection: FileRejection) => string)` | - | Function to generate error messages for rejected files.<br>@example (rejection) => `${rejection.file.path || rejection.file.name}: (${rejection.errors[0].code}) ${rejection.errors[0].message}` |
| **hideSizes** | `boolean` | `false` | If `true`, the size of the files will not be displayed. |
| **iconMapping** | `{ [extension: string]: ReactElement; }` | `{}` | Custom mapping of file extensions to icons<br>Example:<br><br><code>{<br>mp3: \<Icon family="material" name="audio_file" />,<br>png: \<Icon family="material" name="file_png" /><br>}<br></code> |
| **label** | `string` | `'Uploaded Files'` | Title of FilePanel displayed above the list of uploaded files. |
| **onRetry** | `((file: FileWithPath) => void)` | - | Callback fired when the retry button is clicked for a file that failed to upload. |
| **parentFileExtension** | `string ⏐ null` | - | Set which file (e.g. "pdf") extension should be used as the parent for nesting files with the same base name.<br>- If `undefined`, or no file with the specified extension is found, files will nest under the first file alphabetically<br>- If `null`, all nesting will be disabled. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **uploadingFiles** | `FileWithStatus[]` | - | Uploading files that selected from your local computer.<br><br><code>interface FileWithStatus {<br>file: FileWithPath;<br>status: UploadStatus;<br>progress?: number;<br>}<br></code> |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .MIxNovaFilePanel-root | `root` | Class name applied to the root element. |
| .MIxNovaFilePanel-label | `label` | Class name applied to the label displayed above the list of uploaded files. |
| .MIxNovaFilePanel-deletePanel-root | `deletePanel-root` | Class name applied to the root container of the delete panel. |
| .MIxNovaFilePanel-deletePanel-title | `deletePanel-title` | Class name applied to the title of the delete panel. |
| .MIxNovaFilePanel-deletePanel-delete | `deletePanel-delete` | Class name applied to the delete button in the delete panel. |
| .MIxNovaFilePanel-deletePanel-cancel | `deletePanel-cancel` | Class name applied to the cancel button in the delete panel. |

