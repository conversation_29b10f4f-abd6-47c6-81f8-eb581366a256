import { FilePanel, IFilePanel } from '@hxnova/mi-react-components/FilePanel';
import { FileRejection, FileWithPath, FileWithStatus } from '@hxnova/mi-react-components/types';
import { action } from '@storybook/addon-actions';
import { Meta, StoryFn } from '@storybook/react';
import { useEffect, useState } from 'react';
import { getFileNameAndExtension } from '../utils/files';
import { mockAcceptedFiles, mockRejectedFiles, mockUploadingFiles } from '../utils/mockData';

const meta = {
  title: '@hxnova/mi-react-components/Files/FilePanel',
  component: FilePanel,
  parameters: {
    layout: 'padded',
    design: [
      {
        name: 'Contextual',
        type: 'figma',
        url: 'https://www.figma.com/design/vYxc0XelXCJ9bgo3wNC44j/MI-x-Nova-Components?node-id=219-69096&p=f&m=dev',
      },
    ],
  },
} satisfies Meta<typeof FilePanel>;
export default meta;

const Template: StoryFn<IFilePanel> = ({ label, parentFileExtension, hideSizes }) => {
  const [acceptedFiles, setAcceptedFiles] = useState<FileWithPath[]>([]);
  const [uploadingFiles, setUploadingFiles] = useState<FileWithStatus[]>([]);
  const [rejectedFiles, setRejectedFiles] = useState<FileRejection[]>(mockRejectedFiles);

  useEffect(() => {
    const fileCountMap: Record<string, number> = {};

    // Append (1), (2), etc. for duplicate filenames (e.g. "file.txt" -> "file(1).txt")
    const renameFileIfDuplicate = (file: FileWithPath): FileWithPath => {
      const count = (fileCountMap[file.name] = (fileCountMap[file.name] || 0) + 1);

      if (count > 1) {
        const { name, extension } = getFileNameAndExtension(file.name);
        const extensionWithDot = extension ? `.${extension}` : '';
        const newFileName = `${name}(${count - 1})${extensionWithDot}`;
        return new File([file], newFileName, { type: file.type });
      }

      return file;
    };

    const renamedAcceptedFiles = mockAcceptedFiles.map(renameFileIfDuplicate);
    const renamedUploadingFiles = mockUploadingFiles.map((fileWithStatus) => ({
      ...fileWithStatus,
      file: renameFileIfDuplicate(fileWithStatus.file),
    }));

    setAcceptedFiles(renamedAcceptedFiles);
    setUploadingFiles(renamedUploadingFiles);
  }, []);

  const handleRemove: IFilePanel['onRemove'] = (fileToRemove) => {
    action('onRemove')(fileToRemove);
    setAcceptedFiles(acceptedFiles.filter((file) => file.name !== fileToRemove.name));
    setUploadingFiles(uploadingFiles.filter((file) => file.file.name !== fileToRemove.name));
  };

  const handleClose: IFilePanel['onClose'] = (fileToClose) => {
    action('onClose')(fileToClose);
    setRejectedFiles(rejectedFiles.filter((file) => file.file.name !== fileToClose.file.name));
  };

  const handleRetry: IFilePanel['onRetry'] = (fileToRetry) => {
    action('onRetry')(fileToRetry);
  };

  return (
    <FilePanel
      label={label}
      hideSizes={hideSizes}
      parentFileExtension={parentFileExtension}
      acceptedFiles={acceptedFiles}
      rejectedFiles={rejectedFiles}
      uploadingFiles={uploadingFiles}
      onRemove={handleRemove}
      onClose={handleClose}
      onRetry={handleRetry}
    />
  );
};

export const Basic = {
  render: Template,

  args: {
    label: 'Uploaded files',
    hideSizes: false,
    parentFileExtension: '',
  },

  argTypes: {
    label: {
      control: 'text',
    },
    parentFileExtension: {
      control: 'text',
    },
    hideSizes: {
      control: 'boolean',
    },
  },

  parameters: {
    controls: {
      include: ['label', 'hideSizes', 'parentFileExtension'],
    },
  },
};
