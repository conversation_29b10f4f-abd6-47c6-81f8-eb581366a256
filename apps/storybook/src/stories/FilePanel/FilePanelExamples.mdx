import { Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import CustomErrorMessageExample from './Examples/CustomErrorMessageExample';
import CustomErrorMessageExampleSource from './Examples/CustomErrorMessageExample.tsx?raw';
import CustomIconExample from './Examples/CustomIconExample';
import CustomIconExampleSource from './Examples/CustomIconExample.tsx?raw';
import FoundStringParentExtExample from './Examples/FoundStringParentExtExample';
import FoundStringParentExtExampleSource from './Examples/FoundStringParentExtExample.tsx?raw';
import NotFoundStringParentExtExample from './Examples/NotFoundStringParentExtExample';
import NotFoundStringParentExtExampleSource from './Examples/NotFoundStringParentExtExample.tsx?raw';
import NullParentExtExample from './Examples/NullParentExtExample';
import NullParentExtExampleSource from './Examples/NullParentExtExample.tsx?raw';
import UndefinedParentExtExample from './Examples/UndefinedParentExtExample';
import UndefinedParentExtExampleSource from './Examples/UndefinedParentExtExample.tsx?raw';

<Meta title="@hxnova/mi-react-components/Files/FilePanel/Examples" />

The `FilePanel` component displays a list of files selected from the your local computer. It includes:

* A list of `Alert` components to show rejected messages for files in the `rejectedFiles` prop.
* A list of `FileItem` components to show files in the `acceptedFiles` and `uploadingFiles` props.
Files in the `acceptedFiles` prop are sorted by the filenames in the alphabetical order.

## Basic Usage

Below is a basic example of `FilePanel` component that displays rejected, accepted and uploading files from test data
with closing alert and removing file handlers.
Uploaded files with same basename are renamed by appending a number to the end.

<div className="sb-unstyled">
    <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Custom File Icon

By default, all files are displayed with the default file icon. You can customize the icon for specific file extensions by using the `iconMapping` prop.

<div className="sb-unstyled">
    <CustomIconExample />
</div>
<CodeExpand code={CustomIconExampleSource} showBorderTop style={{marginTop: 16}}/>

## Custom Error Message

You can customize the alert message for rejected files using the `rejectedFilesMessage` prop.

<div className="sb-unstyled">
    <CustomErrorMessageExample />
</div>
<CodeExpand code={CustomErrorMessageExampleSource} showBorderTop style={{marginTop: 16}}/>

## Nesting Files by Basename

Accepted files with same basename (e.g. `file.dxf` and `file.pdf`) can be nested together under a "parent" file. There are 3 options for nesting that can be configured via the `parentFileExtension` prop.

#### 1. `parentFileExtension = undefined` (default)

This will nest files under the first file alphabetically.

<div className="sb-unstyled">
    <UndefinedParentExtExample />
</div>
<CodeExpand code={UndefinedParentExtExampleSource} showBorderTop style={{marginTop: 16}}/>

#### 2. `parentFileExtension` is a string

This will nest files nested under the file with the specified extension (e.g `"txt"`).

<div className="sb-unstyled">
    <FoundStringParentExtExample />
</div>
<CodeExpand code={FoundStringParentExtExampleSource} showBorderTop style={{marginTop: 16}}/>

If no files with the specified extension are found, the files with same basename will be nested under the first file alphabetically as when `parentFileExtension` is `undefined`.

<div className="sb-unstyled">
    <NotFoundStringParentExtExample />
</div>
<CodeExpand code={NotFoundStringParentExtExampleSource} showBorderTop style={{marginTop: 16}}/>


#### 3. `parentFileExtension = null`

All nesting will be disabled and files with same basename will be displayed as separate items.

<div className="sb-unstyled">
    <NullParentExtExample />
</div>
<CodeExpand code={NullParentExtExampleSource} showBorderTop style={{marginTop: 16}}/>