import { FilePanel } from '@hxnova/mi-react-components/FilePanel';

const acceptedFiles = [
  new File([], 'AND0001910754.txt', {
    type: 'text/plain',
  }),
  new File([], 'AND0001910754.pdf', {
    type: 'text/plain',
  }),
  new File([], 'AND0001910754.dxf', {
    type: 'text/plain',
  }),
];

export default function UndefinedParentExtExample() {
  return <FilePanel acceptedFiles={acceptedFiles} rejectedFiles={[]} onClose={() => {}} onRemove={() => {}} />;
}
