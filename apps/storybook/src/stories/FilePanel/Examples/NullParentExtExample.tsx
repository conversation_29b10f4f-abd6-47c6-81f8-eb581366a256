import { FilePanel } from '@hxnova/mi-react-components/FilePanel';

const acceptedFiles = [
  new File([], 'AND0001910754.txt', {
    type: 'text/plain',
  }),
  new File([], 'AND0001910754.pdf', {
    type: 'text/plain',
  }),
  new File([], 'AND0001910754.dxf', {
    type: 'text/plain',
  }),
];

export default function NullParentExtExample() {
  return (
    <FilePanel
      parentFileExtension={null}
      acceptedFiles={acceptedFiles}
      rejectedFiles={[]}
      onClose={() => {}}
      onRemove={() => {}}
    />
  );
}
