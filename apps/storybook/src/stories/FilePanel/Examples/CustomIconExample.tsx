import { Icon } from '@hxnova/icons';
import { FilePanel } from '@hxnova/mi-react-components/FilePanel';

const acceptedFiles = [
  new File([], 'AND0001910754.mp3', {
    type: 'text/plain',
  }),
  new File([], 'AND0001910754.png', {
    type: 'text/plain',
  }),
  new File([], 'AND0001910754.dxf', {
    type: 'text/plain',
  }),
];

export default function CustomIconExample() {
  return (
    <FilePanel
      iconMapping={{
        mp3: <Icon family="material" name="audio_file" />,
        png: <Icon family="material" name="file_png" />,
      }}
      rejectedFiles={[]}
      acceptedFiles={acceptedFiles}
      onClose={() => {}}
      onRemove={() => {}}
    />
  );
}
