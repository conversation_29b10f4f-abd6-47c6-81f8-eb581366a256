import { FilePanel } from '@hxnova/mi-react-components/FilePanel';

const rejectedFiles = [
  {
    file: new File([], 'AND0001910755.dxf', {
      type: 'text/plain',
    }),
    errors: [
      { code: 'file-invalid-type', message: 'File type must be image/jpeg,.jpg,.jpeg,image/png,.png,image/gif,.gif' },
    ],
  },
  {
    file: new File([], 'AND0001910756.dxf', {
      type: 'text/plain',
    }),
    errors: [
      { code: 'file-invalid-type', message: 'File type must be image/jpeg,.jpg,.jpeg,image/png,.png,image/gif,.gif' },
    ],
  },
];

export default function CustomErrorMessageExample() {
  return (
    <FilePanel
      getErrorMessage={(rejection) =>
        `${rejection.file.path || rejection.file.name}: (${rejection.errors[0].code}) - ${rejection.errors[0].message}`
      }
      rejectedFiles={rejectedFiles}
      acceptedFiles={[]}
      onClose={() => {}}
      onRemove={() => {}}
    />
  );
}
