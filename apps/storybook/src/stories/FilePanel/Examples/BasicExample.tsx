import { FilePanel } from '@hxnova/mi-react-components/FilePanel';
import { FileRejection, FileWithPath, FileWithStatus, UploadStatus } from '@hxnova/mi-react-components/types';
import { useEffect, useState } from 'react';

const fileContent = 'This is a test file content for the files.';

const rejectedFilesData = [
  {
    file: new File([fileContent], 'AND0001910755.dxf', {
      type: 'text/plain',
    }),
    errors: [
      { code: 'file-invalid-type', message: 'File type must be image/jpeg,.jpg,.jpeg,image/png,.png,image/gif,.gif' },
    ],
  },
  {
    file: new File([fileContent], 'AND0001910756.dxf', {
      type: 'text/plain',
    }),
    errors: [
      { code: 'file-invalid-type', message: 'File type must be image/jpeg,.jpg,.jpeg,image/png,.png,image/gif,.gif' },
    ],
  },
];

const acceptedFilesData = [
  new File([fileContent], 'AND0001910754.dxf', {
    type: 'text/plain',
  }),
  new File([fileContent], 'AND0001910753.dxf', {
    type: 'text/plain',
  }),
  new File([fileContent], 'AND0001910753.dxf', {
    type: 'text/plain',
  }),
  new File([fileContent], 'AND0001910754.pdf', {
    type: 'text/plain',
  }),
  new File([fileContent], 'AND0001910754.mp4', {
    type: 'text/plain',
  }),
];

const uploadingFilesData: FileWithStatus[] = [
  {
    file: new File([fileContent], 'AND0001910753.dxf', {
      type: 'text/plain',
    }),
    status: UploadStatus.Uploading,
    progress: 60,
  },
  {
    file: new File([fileContent], 'AND0001910754.pdf', {
      type: 'text/plain',
    }),
    status: UploadStatus.Failed,
  },
];

export default function BasicExample() {
  const [acceptedFiles, setAcceptedFiles] = useState<FileWithPath[]>([]);
  const [uploadingFiles, setUploadingFiles] = useState<FileWithStatus[]>([]);
  const [rejectedFiles, setRejectedFiles] = useState<FileRejection[]>(rejectedFilesData);

  const getFileNameAndExtension = (fileName: string): { name: string; extension: string } => {
    const lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex === -1) return { name: fileName, extension: '' };

    return {
      name: fileName.slice(0, lastDotIndex),
      extension: fileName.slice(lastDotIndex + 1),
    };
  };

  useEffect(() => {
    const handleDuplicateFilenames = () => {
      const fileCountMap: Record<string, number> = {};

      // Append (1), (2), etc. for duplicate filenames (e.g. "file.txt" -> "file(1).txt")
      const renameFileIfDuplicate = (file: FileWithPath): FileWithPath => {
        const count = (fileCountMap[file.name] = (fileCountMap[file.name] || 0) + 1);

        if (count > 1) {
          const { name, extension } = getFileNameAndExtension(file.name);
          const extensionWithDot = extension ? `.${extension}` : '';
          const newFileName = `${name}(${count - 1})${extensionWithDot}`;
          return new File([file], newFileName, { type: file.type });
        }

        return file;
      };

      const renamedAcceptedFiles = acceptedFilesData.map(renameFileIfDuplicate);
      const renamedUploadingFiles = uploadingFilesData.map((fileWithStatus) => ({
        ...fileWithStatus,
        file: renameFileIfDuplicate(fileWithStatus.file),
      }));

      setAcceptedFiles(renamedAcceptedFiles);
      setUploadingFiles(renamedUploadingFiles);
    };

    handleDuplicateFilenames();
  }, []);

  const handleRemoveFile = (fileToRemove: FileWithPath) => {
    setAcceptedFiles(acceptedFiles.filter((file) => file.name !== fileToRemove.name));
    setUploadingFiles(uploadingFiles.filter((file) => file.file.name !== fileToRemove.name));
  };

  const handleCloseAlert = (rejection: FileRejection) => {
    setRejectedFiles(rejectedFiles.filter((file) => file.file.name !== rejection.file.name));
  };

  const handleRetryUpload = (file: FileWithPath) => {
    console.log(`Retrying upload for file: ${file.name}`);
  };

  return (
    <FilePanel
      acceptedFiles={acceptedFiles}
      rejectedFiles={rejectedFiles}
      uploadingFiles={uploadingFiles}
      onClose={handleCloseAlert}
      onRemove={handleRemoveFile}
      onRetry={handleRetryUpload}
    />
  );
}
