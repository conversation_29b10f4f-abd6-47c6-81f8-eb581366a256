# API Documentation

- [FileItem](#fileitem)

# FileItem

API reference docs for the React FileItem component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import { FileItem } from '@hxnova/mi-react-components/FileItem';
// or
import { FileItem } from '@hxnova/mi-react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **icon*** | `ReactNode` | - | File icon displayed at the start of the FileItem. |
| **name*** | `string` | - | Name of the file. |
| **size*** | `number` | - | File size in bytes. |
| **actions** | `ReactNode` | - | Custom actions element to replace the default cancel icon button. If provided, the `onCancel` callback is ignored. |
| **errorMessage** | `string` | `"Upload failed"` | Error message to display when `status="failed"`. |
| **hideSize** | `boolean` | `false` | If `true`, the size of the file will not be displayed when `status='succeeded'`. |
| **onCancel** | `MouseEventHandler<HTMLButtonElement>` | - | Callback fired when the cancel icon button is clicked. This will be ignored if `actions` is provided. |
| **onRetry** | `MouseEventHandler<HTMLButtonElement>` | - | Callback fired when the retry button is clicked when `status="failed"` |
| **progress** | `number` | `0` | The upload progress percentage, from 0 to 100, when `status="uploading"`. |
| **secondaryAction** | `ReactNode` | - | Element to render at the end of the FileItem.<br>If not provided, a default component will be rendered showing the file size (when `status='succeeded'`) and `FileStatus` component. |
| **status** | `UploadStatus` | `'succeeded'` | The file upload status.<br><br><code>enum UploadStatus {<br>Uploading = 'uploading',<br>Failed = 'failed',<br>Succeeded = 'succeeded',<br>}<br></code> |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

### Inheritance

The props of the Nova [ListItem](https://zeroheight.com/9a7698df1/p/231da9-lists) are also available in `FileItem`.

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .MIxNovaFileItem-root | `root` | Class name applied to the root element. |
| .MIxNovaFileItem-fileName | `fileName` | Class name applied to the file name element. |
| .MIxNovaFileItem-fileSize | `fileSize` | Class name applied to the file size element. |

