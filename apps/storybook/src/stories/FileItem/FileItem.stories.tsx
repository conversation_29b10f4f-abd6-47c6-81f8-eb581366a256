import { Icon } from '@hxnova/icons';
import { FileItem, IFileItem } from '@hxnova/mi-react-components/FileItem';
import { UploadStatus } from '@hxnova/mi-react-components/types';
import { List } from '@hxnova/react-components/List';
import { action } from '@storybook/addon-actions';
import { Meta, StoryFn, StoryObj } from '@storybook/react';

const meta = {
  title: '@hxnova/mi-react-components/Files/FileItem',
  component: FileItem,
  parameters: {
    layout: 'start',
    design: [
      {
        name: 'Contextual',
        type: 'figma',
        url: 'https://www.figma.com/design/vYxc0XelXCJ9bgo3wNC44j/MI-x-Nova-Components?node-id=219-69096&p=f&m=dev',
      },
    ],
  },
} satisfies Meta<typeof FileItem>;
export default meta;

const Template: StoryFn<IFileItem> = (args: IFileItem) => (
  <List>
    <FileItem {...args} />
  </List>
);

type Story = StoryObj<typeof Template>;

export const Basic: Story = {
  render: Template,

  args: {
    icon: <Icon family="material" name="description" />,
    status: UploadStatus.Uploading,
    name: 'AND0001910710.dxf',
    size: 1432,
    progress: 50,
    hideSize: false,
    errorMessage: 'Upload failed',
    onRetry: (e) => {
      action('onRetry')(e);
    },
    onCancel: (e) => {
      action('onCancel')(e);
    },
  },

  argTypes: {
    status: {
      control: 'radio',
      options: [UploadStatus.Uploading, UploadStatus.Succeeded, UploadStatus.Failed],
    },
    hideSize: {
      control: 'boolean',
    },
    progress: {
      control: {
        type: 'range',
        min: 0,
        max: 100,
        step: 1,
      },
    },
    errorMessage: {
      control: 'text',
    },
  },

  parameters: {
    controls: {
      include: ['name', 'size', 'hideSize', 'progress', 'status', 'errorMessage'],
    },
  },
};
