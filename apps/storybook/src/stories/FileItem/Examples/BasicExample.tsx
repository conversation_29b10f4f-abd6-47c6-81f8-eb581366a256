import { Icon } from '@hxnova/icons';
import { FileItem } from '@hxnova/mi-react-components/FileItem';
import { UploadStatus } from '@hxnova/mi-react-components/types';
import { List } from '@hxnova/react-components/List';
import { ListDivider } from '@hxnova/react-components/ListDivider';

export default function BasicExample() {
  return (
    <List
      sx={{
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <FileItem
        status={UploadStatus.Uploading}
        progress={50}
        icon={<Icon family="material" name="description" />}
        name="AND0001910710.dxf"
        size={1432}
      />
      <ListDivider />
      <FileItem
        status={UploadStatus.Failed}
        icon={<Icon family="material" name="description" />}
        name="AND0001910710.dxf"
        size={1432}
      />
      <ListDivider />
      <FileItem
        status={UploadStatus.Succeeded}
        icon={<Icon family="material" name="description" />}
        name="AND0001910710.dxf"
        size={1432}
      />
    </List>
  );
}
