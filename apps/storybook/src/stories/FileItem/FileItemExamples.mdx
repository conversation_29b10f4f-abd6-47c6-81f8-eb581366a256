import { Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';

<Meta title="@hxnova/mi-react-components/Files/FileItem/Examples" />

The `FileItem` component is used to display a single uploaded file from your local computer. It contains an icon, file name, file size (only shown when `status='succeeded'`)
and a secondary action element.

By default, if `secondaryAction` prop is not provided, the component will render a `FileStatus` component that indicates the file's upload status.
All props of `FileStatus` are also available in `FileItem` to configure the status display.

## Basic Usage

Below is a basic example of the `FileItem` component that renders `FileStatus` component as the default secondary action. `FileStatus` comes in 3 different states: `uploading`, `failed`, and `succeeded` (default). This can be set using the `status` prop.
<div className="sb-unstyled">
    <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>