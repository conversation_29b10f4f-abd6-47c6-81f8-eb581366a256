# API Documentation

- [FileStatus](#filestatus)

# FileStatus

API reference docs for the React FileStatus component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import { FileStatus } from '@hxnova/mi-react-components/FileStatus';
// or
import { FileStatus } from '@hxnova/mi-react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **actions** | `ReactNode` | - | Custom actions element to replace the default cancel icon button. If provided, the `onCancel` callback is ignored. |
| **errorMessage** | `string` | `"Upload failed"` | Error message to display when `status="failed"`. |
| **onCancel** | `MouseEventHandler<HTMLButtonElement>` | - | Callback fired when the cancel icon button is clicked. This will be ignored if `actions` is provided. |
| **onRetry** | `MouseEventHandler<HTMLButtonElement>` | - | Callback fired when the retry button is clicked when `status="failed"` |
| **progress** | `number` | `0` | The upload progress percentage, from 0 to 100, when `status="uploading"`. |
| **status** | `UploadStatus` | `'succeeded'` | The file upload status.<br><br><code>enum UploadStatus {<br>Uploading = 'uploading',<br>Failed = 'failed',<br>Succeeded = 'succeeded',<br>}<br></code> |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .MIxNovaFileStatus-root | `root` | Class name applied to the root element. |
| .MIxNovaFileStatus-status | `status` | Class name applied to the container of status indicators. |
| .MIxNovaFileStatus-progress | `progress` | Class name applied to the `LinearProgress` element. |
| .MIxNovaFileStatus-failed | `failed` | Class name applied to the error message text when `status='failed'`. |
| .MIxNovaFileStatus-actions-uploading | `actions-uploading` | Class name applied to the actions element when `status='uploading'`. |
| .MIxNovaFileStatus-actions-failed | `actions-failed` | Class name applied to the actions element when `status='failed'`. |
| .MIxNovaFileStatus-actions-succeeded | `actions-succeeded` | Class name applied to the actions element when `status='succeeded'`. |
| .MIxNovaFileStatus-retry | `retry` | Class name applied to the retry button shown when `status='failed'`. |

