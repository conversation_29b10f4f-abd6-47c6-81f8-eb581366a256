import { Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import CustomActionsExample from './Examples/CustomActionsExample';
import CustomActionsExampleSource from './Examples/CustomActionsExample.tsx?raw';
import StatusExample from './Examples/StatusExample';
import StatusExampleSource from './Examples/StatusExample.tsx?raw';

<Meta title="@hxnova/mi-react-components/Files/FileStatus/Examples" />

The `FileStatus` component is used to indicate the upload status of a file. Each status displays different UI elements, along with an actions element.
By default, if `actions` prop is not provided, the actions element is a cancel icon button, which triggers the `onCancel` callback when clicked.

## Status

`FileStatus` comes in 3 different statuses: `uploading`, `failed`, and `succeeded` (default). This can be set using the `status` prop.

* `uploading` status will show a `LinearProgress` with value of `progress` prop and actions element.
* `failed` status will show the `errorMessage` prop, actions element and a retry button that will call the `onRetry` prop when clicked.
* `succeeded` status will only show the actions element. 

<div className="sb-unstyled">
    <StatusExample />
</div>
<CodeExpand code={StatusExampleSource} showBorderTop style={{marginTop: 16}}/>

## Custom Actions

You can replace the default cancel icon button by providing a custom element to `actions` prop. When this is set, the `onCancel` prop will be ignored.
<div className="sb-unstyled">
    <CustomActionsExample />
</div>
<CodeExpand code={CustomActionsExampleSource} showBorderTop style={{marginTop: 16}}/>