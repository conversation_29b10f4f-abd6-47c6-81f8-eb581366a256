import { FileStatus } from '@hxnova/mi-react-components/FileStatus';
import { UploadStatus } from '@hxnova/mi-react-components/types';
import { Divider } from '@hxnova/react-components/Divider';

export default function StatusExample() {
  return (
    <div
      sx={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        gap: 32,
      }}
    >
      <FileStatus status={UploadStatus.Uploading} progress={50} />
      <Divider variant="inset" orientation="vertical" />
      <FileStatus status={UploadStatus.Failed} />
      <Divider variant="inset" orientation="vertical" />
      <FileStatus status={UploadStatus.Succeeded} />
    </div>
  );
}
