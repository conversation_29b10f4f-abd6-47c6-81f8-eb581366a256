import { Icon } from '@hxnova/icons';
import { FileStatus } from '@hxnova/mi-react-components/FileStatus';
import { UploadStatus } from '@hxnova/mi-react-components/types';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Divider } from '@hxnova/react-components/Divider';

export default function CustomActionsExample() {
  return (
    <div
      sx={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'end',
        gap: 32,
      }}
    >
      <FileStatus
        status={UploadStatus.Uploading}
        progress={50}
        actions={
          <IconButton variant="neutral" onClick={() => console.log('Cancel upload')}>
            <Icon family="material" name="cancel" />
          </IconButton>
        }
      />
      <Divider variant="inset" orientation="vertical" />
      <FileStatus
        status={UploadStatus.Failed}
        actions={
          <IconButton variant="neutral" onClick={() => console.log('Cancel upload')}>
            <Icon family="material" name="cancel" />
          </IconButton>
        }
      />
      <Divider variant="inset" orientation="vertical" />
      <FileStatus
        status={UploadStatus.Succeeded}
        actions={
          <IconButton variant="neutral" onClick={() => console.log('Cancel upload')}>
            <Icon family="material" name="cancel" />
          </IconButton>
        }
      />
    </div>
  );
}
