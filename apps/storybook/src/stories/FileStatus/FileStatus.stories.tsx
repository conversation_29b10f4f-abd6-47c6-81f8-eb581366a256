import { FileStatus } from '@hxnova/mi-react-components/FileStatus';
import { UploadStatus } from '@hxnova/mi-react-components/types';
import { action } from '@storybook/addon-actions';
import { Meta, StoryObj } from '@storybook/react';

const meta = {
  title: '@hxnova/mi-react-components/Files/FileStatus',
  component: FileStatus,
  parameters: {
    layout: 'padded',
    design: [
      {
        name: 'Contextual',
        type: 'figma',
        url: 'https://www.figma.com/design/vYxc0XelXCJ9bgo3wNC44j/MI-x-Nova-Components?node-id=219-69096&p=f&m=dev',
      },
    ],
  },
} satisfies Meta<typeof FileStatus>;
export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {
  args: {
    status: UploadStatus.Uploading,
    progress: 50,
    errorMessage: 'Upload failed',
    onRetry: (e) => {
      action('onRetry')(e);
    },
    onCancel: (e) => {
      action('onCancel')(e);
    },
  },

  argTypes: {
    status: {
      control: 'radio',
      options: [UploadStatus.Uploading, UploadStatus.Succeeded, UploadStatus.Failed],
    },
    errorMessage: {
      control: 'text',
    },
    progress: {
      control: {
        type: 'range',
        min: 0,
        max: 100,
        step: 1,
      },
    },
  },

  parameters: {
    controls: {
      include: ['progress', 'status', 'errorMessage'],
    },
  },
};
