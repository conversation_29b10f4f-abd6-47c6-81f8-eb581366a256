import { Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import LoadingExample from './Examples/LoadingExample';
import LoadingExampleSource from './Examples/LoadingExample.tsx?raw';
import ModeExample from './Examples/ModeExample';
import ModeExampleSource from './Examples/ModeExample.tsx?raw';

<Meta title="@hxnova/mi-react-components/Cards/EmptyCard/Examples" />

## Mode

EmptyCard comes in two modes (shapes): `square` (default) and `rectangle`. It can be adjusted using the `mode` prop.

<div className="sb-unstyled">
    <ModeExample />
</div>
<CodeExpand code={ModeExampleSource} showBorderTop style={{marginTop: 16}}/>

## Loading

EmptyCard has a loading state with 2 modes: `spinner` (default) and `skeleton`. The loading behavior can be controlled using the `loading` and `loadingMode` props.

<div className="sb-unstyled">
  <LoadingExample />
</div>
<CodeExpand code={LoadingExampleSource} showBorderTop style={{marginTop: 16}} />