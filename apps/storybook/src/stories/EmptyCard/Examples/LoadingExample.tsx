import { EmptyCard } from '@hxnova/mi-react-components/EmptyCard';

export default function ModeExample() {
  return (
    <div
      sx={{
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 32,
      }}
    >
      <EmptyCard
        sx={{
          width: 330,
          minHeight: 280,
        }}
        header="Create Project"
        description="Create a new project to get started"
        loading
      />

      <EmptyCard
        sx={{
          width: 330,
          minHeight: 280,
        }}
        header="Create Project"
        description="Create a new project to get started"
        loading
        loadingMode="skeleton"
      />

      <EmptyCard
        sx={{
          width: 446,
        }}
        header="Create Project"
        description="Create a new project to get started"
        mode="rectangle"
        loading
      />

      <EmptyCard
        sx={{
          width: 446,
        }}
        header="Create Project"
        description="Create a new project to get started"
        mode="rectangle"
        loading
        loadingMode="skeleton"
      />
    </div>
  );
}
