# API Documentation

- [EmptyCard](#emptycard)

# EmptyCard

API reference docs for the React EmptyCard component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import { EmptyCard } from '@hxnova/mi-react-components/EmptyCard';
// or
import { EmptyCard } from '@hxnova/mi-react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **description** | `ReactNode` | `''` | The description for additional information. It could be a string or a custom component. |
| **header** | `ReactNode` | `''` | The header of the component. It could be a string or a custom component. |
| **icon** | `ReactNode` | `<Icon family="material" name="add" />` | The Icon element displayed on the top of Card. It could be an Icon or an image. |
| **loading** | `boolean` | `false` | If `true`, a loading indicator will be displayed blocking interaction with the card |
| **loadingMode** | `'spinner' ⏐ 'skeleton'` | `'spinner'` | The style of loader |
| **mode** | `'square' ⏐ 'rectangle'` | `'square'` | Determines the shape of the card |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

### Inheritance

The props of the Nova [Card.Root](https://zeroheight.com/9a7698df1/p/38e9e0-cards/b/65548f) are also available in `EmptyCard`.

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .MIxNovaEmptyCard-root | `root` | Class name applied to the root element. |
| .MIxNovaEmptyCard-rectangleHeader | `rectangleHeader` | Class name applied to the header element when `mode='rectangle'`. Use `.MIxNovaEmptyState-header` to style the header when `mode='square'`. |
| .MIxNovaEmptyCard-description | `description` | Class name applied to the description element. |

