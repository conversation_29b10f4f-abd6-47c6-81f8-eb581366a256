import { EmptyCard, IEmptyCard } from '@hxnova/mi-react-components/EmptyCard';
import { styled } from '@pigment-css/react';
import { Meta, StoryFn, StoryObj } from '@storybook/react';

const meta = {
  title: '@hxnova/mi-react-components/Cards/EmptyCard',
  component: EmptyCard,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/vYxc0XelXCJ9bgo3wNC44j/MI-x-Nova-Components?node-id=217-63166&m=dev',
    },
    controls: { include: ['header', 'description', 'loading', 'loadingMode'] },
  },
  argTypes: {
    header: {
      control: 'text',
    },
    description: {
      control: 'text',
    },
    loading: {
      control: 'boolean',
    },
    loadingMode: {
      control: { type: 'radio' },
      options: ['spinner', 'skeleton'],
    },
  },
} satisfies Meta<typeof EmptyCard>;
export default meta;

const StyledEmptyCard = styled(EmptyCard)<IEmptyCard>(() => ({
  variants: [
    {
      props: { mode: 'square' },
      style: {
        width: 330,
        minHeight: 280,
      },
    },
    {
      props: { mode: 'rectangle' },
      style: {
        width: 446,
      },
    },
  ],
}));

const Template: StoryFn<IEmptyCard> = (props) => {
  return <StyledEmptyCard {...props} />;
};

type Story = StoryObj<typeof Template>;

export const Square: Story = {
  render: Template,

  args: {
    header: 'Create Project',
    description: 'Create a new project to get started',
    mode: 'square',
    loading: false,
    loadingMode: 'spinner',
  },
};

export const Rectangle: Story = {
  render: Template,

  args: {
    header: 'Create Project',
    description: 'Create a new project to get started',
    mode: 'rectangle',
    loading: false,
    loadingMode: 'spinner',
  },
};
