import { Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import ComplexExample from './Examples/ComplexExample';
import ComplexExampleSource from './Examples/ComplexExample.tsx?raw';
import LoadingExample from './Examples/LoadingExample';
import LoadingExampleSource from './Examples/LoadingExample.tsx?raw';

<Meta title="@hxnova/mi-react-components/Cards/ProductCard/Examples" />

## Basic

Below is a basic example of the ProductCard component. It includes a icon, headline and subheading.

<div className="sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}} />

## Complex

Below is a more complex example of the ProductCard component. It includes all the items that can be configured in the ProductCard component.

<div className="sb-unstyled">
  <ComplexExample />
</div>
<CodeExpand code={ComplexExampleSource} showBorderTop style={{marginTop: 16}} />

## Loading

ProductCard has a loading state with 2 modes: `spinner` (default) and `skeleton`. The loading behavior can be controlled using the `loading` and `loadingMode` props.

<div className="sb-unstyled">
  <LoadingExample />
</div>
<CodeExpand code={LoadingExampleSource} showBorderTop style={{marginTop: 16}} />

