import { Icon } from '@hxnova/icons';
import { IProductCard, ProductCard } from '@hxnova/mi-react-components/ProductCard';
import { Button } from '@hxnova/react-components/Button';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Tag } from '@hxnova/react-components/Tag';
import { Typography } from '@hxnova/react-components/Typography';
import MetrologyReportingIcon from '@nexusui/branding/MetrologyReporting';
import NexusLogo from '../../../assets/nexus-logo.svg';

export default function LoadingExample() {
  const menuActions: IProductCard['menuActions'] = [
    {
      children: 'Delete',
      onClick: () => {},
    },
    {
      children: 'Settings',
      onClick: () => {},
    },
  ];

  const content: IProductCard['content'] = (
    <div
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 12,
      }}
    >
      <img
        src={NexusLogo}
        alt="Nexus Logo"
        sx={{
          padding: '8px 10px',
          backgroundColor: '#f3f3f3',
          borderRadius: 12,
        }}
      />
      <Tag intensity="subtle" label="Partner Product" variant="info" />

      <Icon
        family="material"
        name="info"
        size={24}
        filled
        style={{
          color: 'var(--palette-onSurfaceVariant)',
        }}
      />
    </div>
  );

  const listActions: IProductCard['listActions'] = [
    {
      children: (
        <>
          <div
            sx={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              gap: 8,
            }}
          >
            <Tag intensity="subtle" label="Update Available" variant="warning" />
            <Typography variant="labelSmall">v.10.24</Typography>
          </div>
          <ListItemDecorator>
            <Button variant="text" size="small" endIcon={<Icon family="material" name="download" />}>
              Install
            </Button>
          </ListItemDecorator>
        </>
      ),
    },
    {
      text: 'Launch',
      icon: <Icon family="material" name="arrow_outward" />,
      onClick: () => {},
    },
    {
      text: 'Download Title 1',
      icon: <Icon family="material" name="download" />,
      onClick: () => {},
    },
    {
      text: 'Download Title 2',
      icon: <Icon family="material" name="download" />,
      onClick: () => {},
    },
  ];

  return (
    <div style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '2rem', alignItems: 'start' }}>
      <ProductCard
        loading
        loadingMode="spinner"
        icon={<MetrologyReportingIcon />}
        headline="Metrology Reporting"
        subheading="v1.0.0"
        menuActions={menuActions}
        content={content}
        listActions={listActions}
        sx={{
          width: 350,
        }}
      />

      <ProductCard
        loading
        loadingMode="skeleton"
        icon={<></>}
        headline=""
        sx={{
          width: 350,
        }}
      />
    </div>
  );
}
