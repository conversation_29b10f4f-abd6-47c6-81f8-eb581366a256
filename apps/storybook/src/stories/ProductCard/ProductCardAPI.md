# API Documentation

- [ProductCard](#productcard)

# ProductCard

API reference docs for the React ProductCard component. Learn about the props, CSS, and other APIs of this exported module.

## Import

```jsx
import { ProductCard } from '@hxnova/mi-react-components/ProductCard';
// or
import { ProductCard } from '@hxnova/mi-react-components';
```

## Props

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **headline*** | `ReactNode` | - | The main label of the card. It could be a string or a custom component. |
| **icon*** | `ReactNode` | - | The icon element to display.<br>It always shows the Logo of the product. |
| **content** | `ReactNode` | - | The content rendered between the header section and the actions list. It could be a string or a custom component. |
| **listActions** | `(ReactNode ⏐ ListItemProps ⏐ CustomizedListItemProps)[]` | - | The list of actions to display in the `List` component.<br>Each element in the array can be:<br>- A simplified `CustomizedListItemProps` object with required `text` and optional `icon` properties, used to render a customized `ListItem`.<br>- A [ListItemProps]((https://zeroheight.com/9a7698df1/p/231da9-lists/b/723930)) object used to render a standard `ListItem`<br>- A custom component<br><br><code>type CustomizedListItemProps = {<br>text: ReactNode;<br>icon?: ReactNode;<br>onClick: () => void;<br>}<br></code> |
| **loading** | `boolean` | `false` | If `true`, a loading indicator will be displayed blocking interaction with the card |
| **loadingMode** | `'spinner' ⏐ 'skeleton'` | `'spinner'` | The style of loader |
| **menuActions** | `ReactNode ⏐ MenuItemProps[]` | - | The menu actions to display in the top-right `Menu`. This can be:<br>- An array of [MenuItemProps](https://zeroheight.com/9a7698df1/p/531cd9-menu/b/294882) objects used to render `MenuItem` elements in the built-in `Menu` component.<br>- A custom component |
| **subheading** | `ReactNode` | - | A line of text that displays below the card headline. It could be a string or a custom component. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

### Inheritance

The props of the Nova [Card.Root](https://zeroheight.com/9a7698df1/p/38e9e0-cards/b/65548f) are also available in `ProductCard`.

## CSS classes

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .MIxNovaProductCard-root | `root` | Class name applied to the root element. |
| .MIxNovaProductCard-stack | `stack` | Class name applied to the stack element that wraps basic info and content |
| .MIxNovaProductCard-icon | `icon` | Class name applied to the icon element. |
| .MIxNovaProductCard-headline | `headline` | Class name applied to the headline element. |
| .MIxNovaProductCard-subheading | `subheading` | Class name applied to the subheading element. |
| .MIxNovaProductCard-menuItem | `menuItem` | Class name applied to the menu item element. |
| .MIxNovaProductCard-actionDropdownButton | `actionDropdownButton` | Class name applied to the action dropdown button element. |
| .MIxNovaProductCard-actionDropdownMenu | `actionDropdownMenu` | Class name applied to the action dropdown menu element. |
| .MIxNovaProductCard-basicInfoStack | `basicInfoStack` | Class name applied to the basic info stack element that wraps icon, headlineSubheading stack and menu |
| .MIxNovaProductCard-headlineSubheadingStack | `headlineSubheadingStack` | Class name applied to the headline and subheading stack element. |
| .MIxNovaProductCard-content | `content` | Class name applied to the content element. |
| .MIxNovaProductCard-list | `list` | Class name applied to the list element. |
| .MIxNovaProductCard-listItem | `listItem` | Class name applied to the list item element. |
| .MIxNovaProductCard-listItemIcon | `listItemIcon` | Class name applied to the icon element in the customized ListItem. |
| .MIxNovaProductCard-listItemText | `listItemText` | Class name applied to the text element in the customized ListItem. |

