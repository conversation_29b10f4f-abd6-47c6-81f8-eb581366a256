import { Icon } from '@hxnova/icons';
import { IProductCard, ProductCard } from '@hxnova/mi-react-components/ProductCard';
import { Button } from '@hxnova/react-components/Button';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Tag } from '@hxnova/react-components/Tag';
import { Typography } from '@hxnova/react-components/Typography';
import MetrologyReportingIcon from '@nexusui/branding/MetrologyReporting';
import type { Meta, StoryFn, StoryObj } from '@storybook/react';
import NexusLogo from '../../assets/nexus-logo.svg';

const meta = {
  title: '@hxnova/mi-react-components/Cards/ProductCard',
  component: ProductCard,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/vYxc0XelXCJ9bgo3wNC44j/MI-x-Nova-Components?node-id=505-15045&m=dev',
    },
    controls: {
      include: ['headline', 'subheading', 'loading', 'loadingMode', '_showMenu', '_showContent', '_showList'],
    },
  },
  argTypes: {
    headline: {
      control: 'text',
    },
    subheading: {
      control: 'text',
    },
    loading: {
      control: 'boolean',
    },
    loadingMode: {
      control: 'radio',
      options: ['spinner', 'skeleton'],
    },
  },
} satisfies Meta<typeof ProductCard>;
export default meta;

const content: IProductCard['content'] = (
  <div
    sx={(theme) => ({
      display: 'flex',
      alignItems: 'center',
      gap: theme.vars.sys.viewport.spacing.spaceBetween.horizontal.sm,
    })}
  >
    <img
      src={NexusLogo}
      alt="Nexus Logo"
      sx={{
        padding: '8px 10px',
        backgroundColor: '#f3f3f3',
        borderRadius: 12,
      }}
    />
    <Tag intensity="subtle" label="Partner Product" variant="info" />

    <Icon
      family="material"
      name="info"
      size={24}
      filled
      style={{
        color: 'var(--palette-onSurfaceVariant)',
      }}
    />
  </div>
);

const menuActions: IProductCard['menuActions'] = [
  {
    children: 'Delete',
  },
  {
    children: 'Settings',
  },
];

const listActions: IProductCard['listActions'] = [
  {
    children: (
      <>
        <div
          sx={{
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            gap: 8,
          }}
        >
          <Tag intensity="subtle" label="Update Available" variant="warning" />
          <Typography variant="labelSmall">v.10.24</Typography>
        </div>
        <ListItemDecorator>
          <Button variant="text" size="small" endIcon={<Icon family="material" name="download" />}>
            Install
          </Button>
        </ListItemDecorator>
      </>
    ),
  },
  {
    text: 'Launch',
    icon: <Icon family="material" name="arrow_outward" />,
  },
  {
    text: 'Download Title 1',
    icon: <Icon family="material" name="download" />,
  },
  {
    text: 'Download Title 1',
    icon: <Icon family="material" name="download" />,
  },
];

type ExtraControl = {
  _showMenu?: boolean;
  _showContent?: boolean;
  _showList?: boolean;
};

const Template: StoryFn<IProductCard & ExtraControl> = ({ _showContent, _showList, _showMenu, ...restProps }) => {
  return (
    <ProductCard
      content={_showContent ? content : undefined}
      listActions={_showList ? listActions : undefined}
      menuActions={_showMenu ? menuActions : undefined}
      {...restProps}
    />
  );
};

type Story = StoryObj<typeof Template>;

export const Basic: Story = {
  render: Template,
  args: {
    icon: <MetrologyReportingIcon />,
    headline: 'Metrology Reporting',
    subheading: 'v1.0.0',
    loading: false,
    loadingMode: 'spinner',
    style: {
      width: 350,
    },
    _showMenu: false,
    _showContent: false,
    _showList: false,
  },
};

export const Complex: Story = {
  render: Template,
  args: {
    icon: <MetrologyReportingIcon />,
    headline: 'Metrology Reporting',
    subheading: 'v1.0.0',
    loading: false,
    loadingMode: 'spinner',
    style: {
      width: 350,
    },
    _showMenu: true,
    _showContent: true,
    _showList: true,
  },
};
