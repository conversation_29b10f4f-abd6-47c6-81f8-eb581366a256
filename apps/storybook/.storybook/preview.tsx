/* eslint-disable react-hooks/rules-of-hooks */
import '@hxnova/icons/icons.css';
import { CssBaseline } from '@hxnova/react-components/CssBaseline';
import { PickerProvider } from '@hxnova/react-components/DatePickers';
import { NovaProvider } from '@hxnova/react-components/NovaProvider';
import '@hxnova/themes/styles.css';
import '@pigment-css/react/styles.css';
import { DocsContainer } from '@storybook/blocks';
import { useEffect } from 'react';
import { useDarkMode } from 'storybook-dark-mode';
import { getLocale, locales } from './locales';
import { NovaDark, NovaLight } from './theme';

const storybookProvider = (Story, context) => {
  const isDark = useDarkMode();
  const locale = context.globals.locale;
  const colorScheme = isDark ? 'dark' : 'light';
  useEffect(() => {
    document.body.classList.remove('light', 'dark');
    document.body.classList.add(colorScheme);
  }, [colorScheme]);

  return (
    <NovaProvider locale={getLocale(locale)}>
      <PickerProvider>
        <CssBaseline />
        <Story {...context} />
      </PickerProvider>
    </NovaProvider>
  );
};

const docsProvider = (props) => {
  const isDark = useDarkMode();
  const docTheme = isDark ? NovaDark : NovaLight;
  const colorScheme = isDark ? 'dark' : 'light';
  useEffect(() => {
    document.body.classList.remove('light', 'dark');
    document.body.classList.add(colorScheme);
  }, [colorScheme]);
  return <DocsContainer {...props} theme={docTheme} />;
};

export const parameters = {
  layout: 'centered',
  controls: {
    matchers: {
      color: /(background|color)$/i,
      date: /Date$/i,
    },
  },
  options: {
    storySort: (a, b) => {
      const aName = a.title;
      const bName = b.title;

      // Define the main section order
      const sectionOrder = ['@hxnova/mi-react-components'];

      // Extract sections
      const aSection = aName.split('/').slice(0, 2).join('/');
      const bSection = bName.split('/').slice(0, 2).join('/');

      // If different sections, sort by section order
      if (aSection !== bSection) {
        const aIndex = sectionOrder.indexOf(aSection);
        const bIndex = sectionOrder.indexOf(bSection);

        if (aIndex !== -1 && bIndex !== -1) {
          return aIndex - bIndex;
        }
        if (aIndex !== -1) return -1;
        if (bIndex !== -1) return 1;
        return aSection.localeCompare(bSection);
      }

      // Within the same section, prioritize top-level documentation files
      const topLevelDocFiles = [];
      const aIsTopLevelDoc = topLevelDocFiles.some((doc) => aName.includes(doc));
      const bIsTopLevelDoc = topLevelDocFiles.some((doc) => bName.includes(doc));

      if (aIsTopLevelDoc && !bIsTopLevelDoc) return -1;
      if (!aIsTopLevelDoc && bIsTopLevelDoc) return 1;

      // If both are top-level docs, sort by doc order
      if (aIsTopLevelDoc && bIsTopLevelDoc) {
        for (const doc of topLevelDocFiles) {
          const aHasDoc = aName.includes(doc);
          const bHasDoc = bName.includes(doc);
          if (aHasDoc && !bHasDoc) return -1;
          if (!aHasDoc && bHasDoc) return 1;
        }
      }

      // Check if we're in the same component folder (case-insensitive comparison)
      const aParts = aName.split('/');
      const bParts = bName.split('/');

      // Normalize component folder names for comparison (handle case inconsistencies)
      const normalizeComponentName = (name) => name.toLowerCase().replace(/\s+/g, ' ');

      // Get the component folder name (third part of the path)
      const aComponentFolder = aParts.length > 2 ? normalizeComponentName(aParts[2]) : '';
      const bComponentFolder = bParts.length > 2 ? normalizeComponentName(bParts[2]) : '';

      if (aComponentFolder && bComponentFolder && aComponentFolder === bComponentFolder) {
        // Within the same component folder, prioritize API and Examples
        const componentDocFiles = ['ReadMe', 'API', 'Examples'];
        const aStoryName = aParts[aParts.length - 1];
        const bStoryName = bParts[bParts.length - 1];

        const aIsComponentDoc = componentDocFiles.some((doc) => aStoryName.includes(doc));
        const bIsComponentDoc = componentDocFiles.some((doc) => bStoryName.includes(doc));

        if (aIsComponentDoc && !bIsComponentDoc) return -1;
        if (!aIsComponentDoc && bIsComponentDoc) return 1;

        // If both are component docs, sort by component doc order (API first, then Examples)
        if (aIsComponentDoc && bIsComponentDoc) {
          for (const doc of componentDocFiles) {
            const aHasDoc = aStoryName.includes(doc);
            const bHasDoc = bStoryName.includes(doc);
            if (aHasDoc && !bHasDoc) return -1;
            if (!aHasDoc && bHasDoc) return 1;
          }
        }
      }

      // For everything else, sort alphabetically
      return aName.localeCompare(bName);
    },
  },
  docs: {
    container: docsProvider,
  },
  darkMode: {
    classTarget: 'html',
    stylePreview: true,
    // Override the default dark theme
    dark: { ...NovaDark },
    darkClass: 'dark-mode',
    // Override the default light theme
    light: { ...NovaLight },
    lightClass: 'light-mode',
  },
};

export const decorators = [storybookProvider];

export const globalTypes = {
  locale: {
    name: 'Locale',
    description: 'Internationalization locale',
    defaultValue: 'en-US',
    toolbar: {
      icon: 'globe',
      title: 'Locale',
      items: locales,
    },
  },
};
