import enUS from '@hxnova/react-components/Locale/en_US';
import enGB from '@hxnova/react-components/Locale/en_GB';
import frFR from '@hxnova/react-components/Locale/fr_FR';
import deDE from '@hxnova/react-components/Locale/de_DE';
import itIT from '@hxnova/react-components/Locale/it_IT';
import esES from '@hxnova/react-components/Locale/es_ES';
import ptPT from '@hxnova/react-components/Locale/pt_PT';
import ptBR from '@hxnova/react-components/Locale/pt_BR';
import plPL from '@hxnova/react-components/Locale/pl_PL';
import svSE from '@hxnova/react-components/Locale/sv_SE';
import nlNL from '@hxnova/react-components/Locale/nl_NL';
import trTR from '@hxnova/react-components/Locale/tr_TR';
import csCZ from '@hxnova/react-components/Locale/cs_CZ';
import huHU from '@hxnova/react-components/Locale/hu_HU';
import ruRU from '@hxnova/react-components/Locale/ru_RU';
import zhCN from '@hxnova/react-components/Locale/zh_CN';
import zhTW from '@hxnova/react-components/Locale/zh_TW';
import koKR from '@hxnova/react-components/Locale/ko_KR';
import jaJP from '@hxnova/react-components/Locale/ja_JP';
import bgBG from '@hxnova/react-components/Locale/bg_BG';
import daDK from '@hxnova/react-components/Locale/da_DK';
import roRO from '@hxnova/react-components/Locale/ro_RO';
import skSK from '@hxnova/react-components/Locale/sk_SK';
import thTH from '@hxnova/react-components/Locale/th_TH';
import arEG from '@hxnova/react-components/Locale/ar_EG';

import { Locale } from '@hxnova/react-components/Locale';

export const getLocale = (value): Locale => {
  switch (value) {
    case 'en-US':
      return enUS;
    case 'en-GB':
      return enGB;
    case 'fr-FR':
      return frFR;
    case 'de-DE':
      return deDE;
    case 'it-IT':
      return itIT;
    case 'es-ES':
      return esES;
    case 'pt-PT':
      return ptPT;
    case 'pt-BR':
      return ptBR;
    case 'pl-PL':
      return plPL;
    case 'sv-SE':
      return svSE;
    case 'nl-NL':
      return nlNL;
    case 'tr-TR':
      return trTR;
    case 'cs-CZ':
      return csCZ;
    case 'hu-HU':
      return huHU;
    case 'ru-RU':
      return ruRU;
    case 'zh-CN':
      return zhCN;
    case 'zh-TW':
      return zhTW;
    case 'ko-KR':
      return koKR;
    case 'ja-JP':
      return jaJP;
    case 'bg-BG':
      return bgBG;
    case 'da-DK':
      return daDK;
    case 'ro-RO':
      return roRO;
    case 'sk-SK':
      return skSK;
    case 'th-TH':
      return thTH;
    case 'ar-EG':
      return arEG;
    default:
      return enUS;
  }
};

export const locales = [
  { value: 'en-US', right: '🇺🇸', title: 'English (US)' },
  { value: 'en-GB', right: '🇬🇧', title: 'English (UK)' },
  { value: 'fr-FR', right: '🇫🇷', title: 'Français' },
  { value: 'de-DE', right: '🇩🇪', title: 'Deutsch' },
  { value: 'it-IT', right: '🇮🇹', title: 'Italiano' },
  { value: 'es-ES', right: '🇪🇸​', title: 'Español' },
  { value: 'pt-PT', right: '🇵🇹​', title: 'Português(Portugal)' },
  { value: 'pt-BR', right: '🇧🇷​', title: 'Português(Brazil)' },
  { value: 'pl-PL', right: '🇵🇱​', title: 'polski' },
  { value: 'sv-SE', right: '🇸🇻​', title: 'svenska' },
  { value: 'nl-NL', right: '🇳🇱​', title: 'Nederlands' },
  { value: 'tr-TR', right: '🇹🇷​', title: 'Türkçe' },
  { value: 'cs-CZ', right: '​🇨🇿​', title: 'čeština' },
  { value: 'hu-HU', right: '🇭🇺​', title: 'magyar' },
  { value: 'ru-RU', right: '🇷🇺​', title: 'русский' },
  { value: 'zh-CN', right: '🇨🇳​', title: '中文简体' },
  { value: 'zh-TW', right: '🇹🇼​', title: '中文繁體' },
  { value: 'ko-KR', right: '🇰🇷​', title: '한국어' },
  { value: 'ja-JP', right: '🇯🇵​', title: '日本語' },
  { value: 'bg-BG', right: '🇧🇬', title: 'български' },
  { value: 'da-DK', right: '🇩🇰', title: 'dansk' },
  { value: 'ro-RO', right: '🇷🇴', title: 'română' },
  { value: 'sk-SK', right: '🇸🇰', title: 'slovenčina' },
  { value: 'th-TH', right: '🇹🇭', title: 'แบบไทย' },
  { value: 'ar-EG', right: '🇪🇬', title: 'العربية (مصر)' },
];
