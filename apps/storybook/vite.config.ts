import { NovaTheme } from '@hxnova/themes';
import { extendTheme, pigment } from '@pigment-css/vite-plugin';
import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';
import rawSourcePlugin from './rawSourcePlugin';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    rawSourcePlugin(),
    pigment({
      theme: extendTheme(NovaTheme),
      transformLibraries: ['@hxnova/mi-react-components', '@hxnova/react-components'],
      babelOptions: {
        plugins: ['@babel/plugin-transform-export-namespace-from'],
      },
    }),
    react({
      babel: {
        plugins: ['@babel/plugin-transform-export-namespace-from'],
      },
    }),
  ],
  optimizeDeps: {
    include: ['react-is', '@mui/utils', 'hoist-non-react-statics', '@base-ui-components/react'],
    exclude: ['@hxnova/themes', '@hxnova/mi-react-components', '@hxnova/react-components'],
  },
});
