export type UpdateNameType = {
  name: string;
  updatedName: string;
};

export const getFileNameAndExtension = (fileName: string): { name: string; extension: string } => {
  const lastDotIndex = fileName.lastIndexOf('.');
  if (lastDotIndex === -1) return { name: fileName, extension: '' };

  return {
    name: fileName.slice(0, lastDotIndex),
    extension: fileName.slice(lastDotIndex + 1),
  };
};

export const simulateUploadProgress = (onProgressUpdate: (newProgress: number) => void) => {
  let progress = 0;
  const interval = setInterval(() => {
    progress += 10;
    onProgressUpdate(progress);
    if (progress >= 100) {
      clearInterval(interval);
    }
  }, 100);
};
