import { Icon } from '@hxnova/icons';
import { Button } from '@hxnova/react-components/Button';
import type { Locale, LocaleCode } from '@hxnova/react-components/Locale';
import arEG from '@hxnova/react-components/Locale/ar_EG';
import bgBG from '@hxnova/react-components/Locale/bg_BG';
import csCZ from '@hxnova/react-components/Locale/cs_CZ';
import daDK from '@hxnova/react-components/Locale/da_DK';
import deDE from '@hxnova/react-components/Locale/de_DE';
import enGB from '@hxnova/react-components/Locale/en_GB';
import enUS from '@hxnova/react-components/Locale/en_US';
import esES from '@hxnova/react-components/Locale/es_ES';
import frFR from '@hxnova/react-components/Locale/fr_FR';
import huHU from '@hxnova/react-components/Locale/hu_HU';
import itIT from '@hxnova/react-components/Locale/it_IT';
import jaJP from '@hxnova/react-components/Locale/ja_JP';
import koKR from '@hxnova/react-components/Locale/ko_KR';
import nlNL from '@hxnova/react-components/Locale/nl_NL';
import plPL from '@hxnova/react-components/Locale/pl_PL';
import ptBR from '@hxnova/react-components/Locale/pt_BR';
import ptPT from '@hxnova/react-components/Locale/pt_PT';
import roRO from '@hxnova/react-components/Locale/ro_RO';
import ruRU from '@hxnova/react-components/Locale/ru_RU';
import skSK from '@hxnova/react-components/Locale/sk_SK';
import svSE from '@hxnova/react-components/Locale/sv_SE';
import thTH from '@hxnova/react-components/Locale/th_TH';
import trTR from '@hxnova/react-components/Locale/tr_TR';
import zhCN from '@hxnova/react-components/Locale/zh_CN';
import zhTW from '@hxnova/react-components/Locale/zh_TW';
import { Menu, menuClasses } from '@hxnova/react-components/Menu';
import { MenuItem } from '@hxnova/react-components/MenuItem';
import * as React from 'react';

const locales: Array<{
  value: LocaleCode;
  right: string;
  title: string;
  locale: Locale;
}> = [
  { value: 'en-US', right: '🇺🇸', title: 'English (US)', locale: enUS },
  { value: 'en-GB', right: '🇬🇧', title: 'English (UK)', locale: enGB },
  { value: 'fr-FR', right: '🇫🇷', title: 'Français', locale: frFR },
  { value: 'de-DE', right: '🇩🇪', title: 'Deutsch', locale: deDE },
  { value: 'it-IT', right: '🇮🇹', title: 'Italiano', locale: itIT },
  { value: 'es-ES', right: '🇪🇸​', title: 'Español', locale: esES },
  { value: 'pt-PT', right: '🇵🇹​', title: 'Português(Portugal)', locale: ptPT },
  { value: 'pt-BR', right: '🇧🇷​', title: 'Português(Brazil)', locale: ptBR },
  { value: 'pl-PL', right: '🇵🇱​', title: 'polski', locale: plPL },
  { value: 'sv-SE', right: '🇸🇻​', title: 'svenska', locale: svSE },
  { value: 'nl-NL', right: '🇳🇱​', title: 'Nederlands', locale: nlNL },
  { value: 'tr-TR', right: '🇹🇷​', title: 'Türkçe', locale: trTR },
  { value: 'cs-CZ', right: '​🇨🇿​', title: 'čeština', locale: csCZ },
  { value: 'hu-HU', right: '🇭🇺​', title: 'magyar', locale: huHU },
  { value: 'ru-RU', right: '🇷🇺​', title: 'русский', locale: ruRU },
  { value: 'zh-CN', right: '🇨🇳​', title: '中文简体', locale: zhCN },
  { value: 'zh-TW', right: '🇹🇼​', title: '中文繁體', locale: zhTW },
  { value: 'ko-KR', right: '🇰🇷​', title: '한국어', locale: koKR },
  { value: 'ja-JP', right: '🇯🇵​', title: '日本語', locale: jaJP },
  { value: 'bg-BG', right: '🇧🇬', title: 'български', locale: bgBG },
  { value: 'da-DK', right: '🇩🇰', title: 'dansk', locale: daDK },
  { value: 'ro-RO', right: '🇷🇴', title: 'română', locale: roRO },
  { value: 'sk-SK', right: '🇸🇰', title: 'slovenčina', locale: skSK },
  { value: 'th-TH', right: '🇹🇭', title: 'แบบไทย', locale: thTH },
  { value: 'ar-EG', right: '🇪🇬', title: 'العربية (مصر)', locale: arEG },
];

export default function LocaleToggleButton({ onLocaleToggle }: { onLocaleToggle: (locale: Locale) => void }) {
  const [title, setTitle] = React.useState('English (US)');
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <div>
      <Button
        variant="text"
        id="basic-button"
        aria-controls={open ? 'basic-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleClick}
        sx={{ position: 'absolute', top: 20, right: 80 }}
        startIcon={<Icon family="material" name="language" />}
      >
        {title}
      </Button>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        sx={{
          [`& .${menuClasses.listbox}`]: {
            maxHeight: 350,
          },
        }}
      >
        {locales.map((i) => (
          <MenuItem
            key={i.value}
            onClick={() => {
              setTitle(i.title);
              onLocaleToggle(i.locale);
              handleClose();
            }}
          >
            {i.title}
          </MenuItem>
        ))}
      </Menu>
    </div>
  );
}
