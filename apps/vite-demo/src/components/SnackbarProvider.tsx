import { Alert } from '@hxnova/react-components/Alert';
import { Snackbar } from '@hxnova/react-components/Snackbar';
import { createContext, PropsWithChildren, ReactNode, useContext, useState } from 'react';

type TriggerSnackbarFn = (message: ReactNode) => void;

const SnackbarContext = createContext<{
  triggerSnackbar: TriggerSnackbarFn;
}>({
  triggerSnackbar: () => {},
});

export const SnackbarProvider = ({ children }: PropsWithChildren) => {
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState<ReactNode>('');

  const triggerSnackbar: TriggerSnackbarFn = (msg) => {
    setMessage(msg);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <SnackbarContext.Provider value={{ triggerSnackbar }}>
      {children}

      <Snackbar open={open} onClose={handleClose}>
        <Alert
          onClose={handleClose}
          sx={{
            minWidth: 200,
          }}
        >
          {message}
        </Alert>
      </Snackbar>
    </SnackbarContext.Provider>
  );
};

// eslint-disable-next-line react-refresh/only-export-components
export const useSnackbar = () => {
  const context = useContext(SnackbarContext);
  if (!context) throw new Error('useSnackbar must be used within a SnackbarProvider');
  return context;
};
