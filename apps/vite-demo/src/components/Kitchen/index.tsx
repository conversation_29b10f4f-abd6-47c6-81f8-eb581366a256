import { lazy } from 'react';

export const kitchenComponentsMap = {
  LoadingPanel: lazy(() => import('./LoadingPanelKitchen')),
  PlainCard: lazy(() => import('./PlainCardKitchen')),
  EmptyState: lazy(() => import('./EmptyStateKitchen')),
  ActionGroup: lazy(() => import('./ActionGroupKitchen')),
  NavBar: lazy(() => import('./NavBarKitchen')),
  EmptyCard: lazy(() => import('./EmptyCardKitchen')),
  ProductCard: lazy(() => import('./ProductCardKitchen')),
  AppSwitcher: lazy(() => import('./AppSwitcherKitchen')),
  FileStatus: lazy(() => import('./FileStatusKitchen')),
  FileItem: lazy(() => import('./FileItemKitchen')),
  FilePanel: lazy(() => import('./FilePanelKitchen')),
  FilePicker: lazy(() => import('./FilePickerKitchen')),
};

export const sortedKitchenLabels = Object.keys(kitchenComponentsMap).sort();
