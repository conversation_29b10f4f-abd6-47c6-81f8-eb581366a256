import { Icon } from '@hxnova/icons';
import { IFilePicker } from '@hxnova/mi-react-components';
import { FilePanel } from '@hxnova/mi-react-components/FilePanel';
import { FilePicker } from '@hxnova/mi-react-components/FilePicker';
import { Button } from '@hxnova/react-components/Button';
import { Dialog, dialogRootClasses } from '@hxnova/react-components/Dialog';
import { List } from '@hxnova/react-components/List';
import { ListDivider } from '@hxnova/react-components/ListDivider';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { Tabs } from '@hxnova/react-components/Tabs';
import { TextField } from '@hxnova/react-components/TextField';
import { Typography } from '@hxnova/react-components/Typography';
import { useState } from 'react';
import { useFilePicker } from '../../hooks/useFilePicker';
import { useFilePickerWithUpload } from '../../hooks/useFilePickerWithUpload';

const dropZoneOptions: IFilePicker['dropZoneOptions'] = {
  accept: {
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/gif': ['.gif'],
    'application/zip': ['.zip', '.7z'],
  },
  noClick: true,
  noKeyboard: true,
  maxFiles: 100,
  maxSize: 2 * 1024 * 1024, // 2MB
  multiple: true,
};

function BasicFilePicker() {
  const { FilePanelProps, handleDrop, isDropped } = useFilePickerWithUpload();

  return (
    <FilePicker
      onDropped={handleDrop}
      dropZoneOptions={dropZoneOptions}
      DropZoneComponentProps={{
        condensed: isDropped,
        header: 'Upload Files',
        showDescription: true,
        showMaxFiles: true,
        acceptedFilesDisplayList: ['jpg', 'jpeg', 'png', 'gif', 'zip', '7z'],
        FilePanelComponent: () => isDropped && <FilePanel {...FilePanelProps} />,
      }}
      DropOverlayComponentProps={{
        header: 'Drop files anywhere to upload.',
        showMaxFiles: true,
        showMaxSize: true,
      }}
    />
  );
}

function DialogFilePicker() {
  const [open, setOpen] = useState(false);

  const { isDropped, FilePanelProps, handleDrop } = useFilePicker();

  return (
    <>
      <Button onClick={() => setOpen(true)}>Open Dialog</Button>
      <Dialog.Root
        open={open}
        onClose={() => setOpen(false)}
        sx={{
          [`& .${dialogRootClasses.paper}`]: {
            maxWidth: 600,
            width: '100%',
          },
        }}
      >
        <Dialog.Header>Add Project</Dialog.Header>
        <Dialog.Content topDivider bottomDivider>
          <TextField
            label="Project Name"
            placeholder="Enter a name for new project"
            fullWidth
            sx={{ marginBottom: 8 }}
          />
          <FilePicker
            onDropped={handleDrop}
            dropZoneOptions={dropZoneOptions}
            DropZoneComponentProps={{
              condensed: isDropped,
              header: 'Upload Files',
              acceptedFilesDisplayList: ['jpg', 'jpeg', 'png', 'gif', 'zip', '7z'],
              FilePanelComponent: () => isDropped && <FilePanel {...FilePanelProps} />,
            }}
            sx={{
              width: '100%',
              overflow: 'unset',
            }}
          />
        </Dialog.Content>
        <Dialog.Actions>
          <Button variant="text" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="filled"
            onClick={() => {
              setOpen(false);
            }}
          >
            Save
          </Button>
        </Dialog.Actions>
      </Dialog.Root>
    </>
  );
}

function ListItemFilePicker() {
  const { handleDrop, FilePanelProps } = useFilePicker();

  return (
    <div>
      <FilePicker
        onDropped={handleDrop}
        dropZoneOptions={dropZoneOptions}
        DropZoneComponent={({ dropZoneProps, buttonLabel }) => (
          <List>
            <ListItem>
              <ListItemContent primary="Cases" />
              <Button variant="text" endIcon={<Icon family="material" name="add" />} onClick={dropZoneProps?.open}>
                {buttonLabel}
              </Button>
            </ListItem>
            <ListDivider />
          </List>
        )}
        DropZoneComponentProps={{
          buttonLabel: 'Add Cases',
        }}
        DropOverlayComponent={({ header, dropZoneOptions }) => (
          <div
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'absolute',
              inset: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.54)',
              border: '2px dashed white',
              zIndex: 1201,
              color: 'white',
            }}
          >
            <Typography>
              {header} - Max Files: {dropZoneOptions?.maxFiles}
            </Typography>
          </div>
        )}
        DropOverlayComponentProps={{
          header: 'Drop files in this ListItem',
        }}
      />

      <FilePanel
        sx={{
          marginTop: 24,
        }}
        {...FilePanelProps}
      />
    </div>
  );
}

export default function FilePickerKitchen() {
  return (
    <Tabs.Root defaultValue={1}>
      <Tabs.List
        sx={{
          marginBottom: 12,
        }}
      >
        <Tabs.Tab value={1}>Basic</Tabs.Tab>
        <Tabs.Tab value={2}>Dialog</Tabs.Tab>
        <Tabs.Tab value={3}>ListItem</Tabs.Tab>
        <Tabs.Indicator />
      </Tabs.List>
      <Tabs.Panel value={1} sx={{ padding: 16 }}>
        <BasicFilePicker />
      </Tabs.Panel>
      <Tabs.Panel value={2} sx={{ padding: 16 }}>
        <DialogFilePicker />
      </Tabs.Panel>
      <Tabs.Panel value={3} sx={{ padding: 16 }}>
        <ListItemFilePicker />
      </Tabs.Panel>
    </Tabs.Root>
  );
}
