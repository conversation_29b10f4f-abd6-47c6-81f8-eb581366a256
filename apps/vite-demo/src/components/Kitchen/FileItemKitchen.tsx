import { Icon } from '@hxnova/icons';
import { FileItem, IFileItem } from '@hxnova/mi-react-components/FileItem';
import { UploadStatus } from '@hxnova/mi-react-components/types';
import { IconButton } from '@hxnova/react-components/IconButton';
import { List } from '@hxnova/react-components/List';
import { ListDivider } from '@hxnova/react-components/ListDivider';
import { useSnackbar } from '../SnackbarProvider';

function FileItemTemplate(props: Partial<IFileItem>) {
  const { triggerSnackbar } = useSnackbar();

  return (
    <FileItem
      icon={<Icon family="material" name="description" />}
      name="AND0001910710.dxf"
      size={1432}
      onRetry={() => triggerSnackbar('Retrying upload')}
      onCancel={() => triggerSnackbar('Cancelling upload')}
      {...props}
    />
  );
}

export default function FileItemKitchen() {
  const { triggerSnackbar } = useSnackbar();

  return (
    <List
      sx={{
        marginTop: 32,
      }}
    >
      <FileItemTemplate name="Uploading" status={UploadStatus.Uploading} progress={50} />
      <ListDivider />
      <FileItemTemplate name="Failed" status={UploadStatus.Failed} />
      <ListDivider />
      <FileItemTemplate name="Succeeded" status={UploadStatus.Succeeded} />
      <ListDivider />
      <FileItemTemplate name="hideSize" hideSize status={UploadStatus.Succeeded} />
      <ListDivider />
      <FileItemTemplate
        name="Custom actions"
        status={UploadStatus.Failed}
        errorMessage="Custom error message"
        actions={
          <IconButton variant="neutral" onClick={() => triggerSnackbar('Custom cancel action')}>
            <Icon family="material" name="cancel" />
          </IconButton>
        }
      />
    </List>
  );
}
