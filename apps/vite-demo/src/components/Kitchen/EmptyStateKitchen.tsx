import { Icon } from '@hxnova/icons';
import { EmptyState } from '@hxnova/mi-react-components/EmptyState';
import { Button } from '@hxnova/react-components/Button';
import { Typography } from '@hxnova/react-components/Typography';
import Heading from '../Heading';

export default function EmptyStateKitchen() {
  return (
    <div>
      {(['medium', 'small'] as const).map((size) => (
        <>
          <Heading>size = {size}</Heading>
          <EmptyState
            sx={{
              marginBlock: 36,
            }}
            key={size}
            size={size}
            header="You don't have access to this content."
            description="Currently you don't have permission to view this content."
            icon={<Icon family="material" name="lock" />}
            actions={[
              <Button variant="filled" key="return">
                Return to dashboard
              </Button>,
              <Button variant="outlined" key="request">
                Request access
              </Button>,
            ]}
          />
        </>
      ))}

      <Heading>Custom</Heading>
      <EmptyState
        sx={{
          marginBlock: 36,
        }}
        icon={<Icon family="material" name="folder" size={72} />}
        header="Currently you don't have anything in your Nexus mounted drive."
        description={
          <div
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: 8,
            }}
          >
            <Typography>
              Open the File Explorer
              {
                <Button
                  sx={{ marginInline: 8 }}
                  variant="outlined"
                  startIcon={<Icon family="material" name="folder" />}
                >
                  Nexus Drive
                </Button>
              }
              to start adding documents.
            </Typography>
            <Typography> Or save documents directly from your desktop application to the Nexus Drive.</Typography>
          </div>
        }
      />
    </div>
  );
}
