import { FileStatus } from '@hxnova/mi-react-components/FileStatus';
import { UploadStatus } from '@hxnova/mi-react-components/types';
import { Button } from '@hxnova/react-components/Button';
import { Typography } from '@hxnova/react-components/Typography';
import { useEffect, useState } from 'react';
import { Fragment } from 'react/jsx-runtime';
import Heading from '../Heading';
import { useSnackbar } from '../SnackbarProvider';

export default function FileStatusKitchen() {
  const { triggerSnackbar } = useSnackbar();
  const [progress, setProgress] = useState(50);

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          return 0;
        }
        return prev + 10;
      });
    }, 500);

    return () => clearInterval(interval);
  });

  return (
    <div>
      <div
        sx={{
          width: 300,
          display: 'grid',
          gridTemplateColumns: '100px 1fr',
          rowGap: 24,
          alignItems: 'center',
          marginBottom: 42,
        }}
      >
        {Object.values(UploadStatus).map((status) => (
          <Fragment key={status}>
            <Typography variant="bodyLarge">{status}</Typography>
            <FileStatus
              status={status}
              progress={progress}
              onRetry={() => {
                triggerSnackbar('Retrying upload...');
              }}
              onCancel={() => {
                triggerSnackbar('Upload cancelled');
              }}
            />
          </Fragment>
        ))}
      </div>

      <div>
        <Heading>Custom Cancel</Heading>
        <div
          sx={{
            width: 400,
          }}
        >
          <FileStatus
            status={UploadStatus.Failed}
            errorMessage="Failed to upload file"
            actions={
              <Button
                variant="outlined"
                size="small"
                onClick={() => triggerSnackbar('Cancel by clicking custom action')}
              >
                Click to Cancel
              </Button>
            }
            onRetry={() => {
              triggerSnackbar('Retrying upload...');
            }}
          />
        </div>
      </div>
    </div>
  );
}
