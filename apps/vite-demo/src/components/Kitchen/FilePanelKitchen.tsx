import { Icon } from '@hxnova/icons';
import { FilePanel, IFilePanel } from '@hxnova/mi-react-components/FilePanel';
import { FileRejection, FileWithPath, FileWithStatus } from '@hxnova/mi-react-components/types';
import { useState } from 'react';
import {
  mockAcceptedFiles,
  mockAcceptedFilesForNesting,
  mockRejectedFiles,
  mockUploadingFiles,
} from '../../utils/mockData';
import Heading from '../Heading';
import { useSnackbar } from '../SnackbarProvider';

function BasicFilePanel() {
  const { triggerSnackbar } = useSnackbar();

  const [acceptedFiles, setAcceptedFiles] = useState<FileWithPath[]>(mockAcceptedFiles);
  const [uploadingFiles, setUploadingFiles] = useState<FileWithStatus[]>(mockUploadingFiles);
  const [rejectedFiles, setRejectedFiles] = useState<FileRejection[]>(mockRejectedFiles);

  const handleRemove: IFilePanel['onRemove'] = (fileToRemove) => {
    setAcceptedFiles(acceptedFiles.filter((file) => file.name !== fileToRemove.name));
    setUploadingFiles(uploadingFiles.filter((file) => file.file.name !== fileToRemove.name));
  };

  const handleClose: IFilePanel['onClose'] = (fileToClose) => {
    setRejectedFiles(rejectedFiles.filter((file) => file.file.name !== fileToClose.file.name));
  };

  const handleRetry: IFilePanel['onRetry'] = (fileToRetry) => {
    triggerSnackbar(`Retrying upload for ${fileToRetry.name}`);
  };

  return (
    <FilePanel
      acceptedFiles={acceptedFiles}
      rejectedFiles={rejectedFiles}
      uploadingFiles={uploadingFiles}
      onRemove={handleRemove}
      onClose={handleClose}
      onRetry={handleRetry}
      sx={{
        marginBottom: 32,
      }}
    />
  );
}

function FilePanelTemplate(props: Partial<IFilePanel>) {
  const { triggerSnackbar } = useSnackbar();

  return (
    <FilePanel
      acceptedFiles={[]}
      rejectedFiles={[]}
      uploadingFiles={[]}
      onRemove={() => {
        triggerSnackbar('onRemove');
      }}
      onClose={() => {
        triggerSnackbar('onClose');
      }}
      onRetry={() => {
        triggerSnackbar('onRetry');
      }}
      sx={{
        marginBottom: 32,
      }}
      {...props}
    />
  );
}

export default function FilePanelKitchen() {
  return (
    <div
      sx={{
        marginTop: 32,
      }}
    >
      <Heading>Basic</Heading>
      <BasicFilePanel />

      <Heading>Custom Error Message</Heading>
      <FilePanelTemplate
        getErrorMessage={(rejection) => `Custom error message for ${rejection.file.name}`}
        rejectedFiles={[mockRejectedFiles[0]]}
      />

      <Heading>Custom Icons</Heading>
      <FilePanelTemplate
        acceptedFiles={mockAcceptedFiles}
        iconMapping={{
          dxf: <Icon family="material" name="architecture" />,
          pdf: <Icon family="material" name="picture_as_pdf" />,
        }}
      />

      <Heading>Nesting</Heading>
      <FilePanelTemplate label="parentFileExtension = undefined" acceptedFiles={mockAcceptedFilesForNesting} />
      <FilePanelTemplate
        label="parentFileExtension = txt"
        parentFileExtension="txt"
        acceptedFiles={mockAcceptedFilesForNesting}
      />
      <FilePanelTemplate
        label="parentFileExtension = null"
        parentFileExtension={null}
        acceptedFiles={mockAcceptedFilesForNesting}
      />

      <Heading>Hide Sizes</Heading>
      <FilePanelTemplate acceptedFiles={mockAcceptedFiles} hideSizes />
    </div>
  );
}
