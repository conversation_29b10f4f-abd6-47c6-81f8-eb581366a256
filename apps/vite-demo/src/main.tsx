import '@hxnova/icons/icons.css';
import '@hxnova/mi-react-components/augment.d.ts';
import { CssBaseline } from '@hxnova/react-components/CssBaseline';
import '@hxnova/themes/styles.css';
import '@pigment-css/react/styles.css';
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <CssBaseline>
      <App />
    </CssBaseline>
  </StrictMode>,
);
