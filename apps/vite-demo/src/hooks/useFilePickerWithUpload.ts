import { IFilePanel } from '@hxnova/mi-react-components/FilePanel';
import { IFilePicker } from '@hxnova/mi-react-components/FilePicker';
import { FileRejection, FileWithPath, FileWithStatus, UploadStatus } from '@hxnova/mi-react-components/types';
import { useCallback, useMemo, useState } from 'react';
import { getFileNameAndExtension, simulateUploadProgress, UpdateNameType } from '../utils/files';

/**
 * Hook to manage state and logic for FilePicker component with upload simulation.
 * - Tracks accepted, rejected, and uploading files.
 * - Simulates file upload progress and randomly triggers success or failure.
 * - Defines handlers for file drop, retry upload, file removal, and rejection alert closure.
 * - Renames files that have same names with uploaded ones when dropping.
 * - Packages files lists and handlers into `FilePanelProps` for use in the FilePanel component.
 */

export const useFilePickerWithUpload = () => {
  const [isDropped, setIsDropped] = useState(false);
  const [acceptedFiles, setAcceptedFiles] = useState<FileWithPath[]>([]);
  const [rejectedFiles, setRejectedFiles] = useState<FileRejection[]>([]);
  const [uploadingFiles, setUploadingFiles] = useState<FileWithStatus[]>([]);
  const [uploadedFileNames, setUploadedFileNames] = useState<UpdateNameType[]>([]);

  const uploadFile = useCallback((fileToUpload: FileWithStatus) => {
    simulateUploadProgress((progress) => {
      if (progress >= 100) {
        // 50% chance of upload failed to test retry upload
        const random = Math.random();
        if (random < 0.5) {
          setUploadingFiles((prev) => prev.filter((fw) => fw.file !== fileToUpload.file));
          setAcceptedFiles((prev) => [...prev, fileToUpload.file]);
        } else {
          setUploadingFiles((prev) => {
            return prev.map((fw) => (fw.file === fileToUpload.file ? { ...fw, status: UploadStatus.Failed } : fw));
          });
        }
      }
    });
  }, []);

  const renameFileIfDuplicate = useCallback(
    (file: File): File => {
      const { name, extension } = getFileNameAndExtension(file.name);
      const duplicateCount = uploadedFileNames.filter((r) => r.name === file.name).length;

      if (duplicateCount === 0) return file;

      const extensionWithDot = extension ? `.${extension}` : '';
      const newFileName = `${name}(${duplicateCount})${extensionWithDot}`;
      return new File([file], newFileName, { type: file.type });
    },
    [uploadedFileNames],
  );

  const handleDrop: IFilePicker['onDropped'] = useCallback(
    (acceptedFiles, rejectedFiles) => {
      setIsDropped(true);
      setRejectedFiles((prev) => [...prev, ...rejectedFiles]);
      acceptedFiles.forEach((file) => {
        const renamedFile = renameFileIfDuplicate(file);
        const newUploadedFileName: UpdateNameType = { name: file.name, updatedName: renamedFile.name };
        setUploadedFileNames((prev) => [...prev, newUploadedFileName]);

        const fileToUpload = {
          file: renamedFile,
          status: UploadStatus.Uploading,
          progress: 0,
        };
        setUploadingFiles((prev) => [...prev, fileToUpload]);
        uploadFile(fileToUpload);
      });
    },
    [uploadFile, renameFileIfDuplicate],
  );

  const handleRetryUpload = useCallback(
    (file: FileWithPath) => {
      setUploadingFiles((prev) => {
        const fileIndex = prev.findIndex((fw) => fw.file === file);
        const newUploadingFiles = [...prev];
        const fileToRetry = {
          ...newUploadingFiles[fileIndex],
          status: UploadStatus.Uploading,
          progress: 0,
        };
        newUploadingFiles[fileIndex] = fileToRetry;

        uploadFile(fileToRetry);

        return newUploadingFiles;
      });
    },
    [uploadFile],
  );

  const handleRemoveFile = useCallback((file: FileWithPath) => {
    setAcceptedFiles((prev) => prev.filter((fw) => fw !== file));
    setUploadingFiles((prev) => prev.filter((fw) => fw.file !== file));
    setUploadedFileNames((prev) => prev.filter((names) => names.updatedName !== file.name));
  }, []);

  const handleCloseAlert = useCallback((rejection: FileRejection) => {
    setRejectedFiles((prev) => prev.filter((fw) => fw.file.name !== rejection.file.name));
  }, []);

  const FilePanelProps: IFilePanel = useMemo(
    () => ({
      acceptedFiles,
      rejectedFiles,
      uploadingFiles,
      onRemove: handleRemoveFile,
      onClose: handleCloseAlert,
      onRetry: handleRetryUpload,
      getErrorMessage: (rejection) => `Failed to upload ${rejection.file.name} - ${rejection.errors[0].message}`,
    }),
    [acceptedFiles, rejectedFiles, uploadingFiles, handleRemoveFile, handleCloseAlert, handleRetryUpload],
  );

  return {
    FilePanelProps,
    handleDrop,
    isDropped,
  };
};
