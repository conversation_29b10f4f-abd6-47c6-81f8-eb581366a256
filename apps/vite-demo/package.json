{"name": "vite-demo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@hxnova/icons": "1.0.0-alpha.0", "@hxnova/mi-react-components": "workspace:*", "@hxnova/react-components": "1.0.0-alpha.10", "@hxnova/themes": "1.0.0-alpha.11", "@nexusui/branding": "^2.10.0", "@pigment-css/react": "^0.0.30", "hoist-non-react-statics": "^3.3.2", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-is": "^18.2.0"}, "devDependencies": {"@babel/plugin-transform-export-namespace-from": "^7.27.1", "@pigment-css/vite-plugin": "^0.0.30", "@types/node": "^22.13.10", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.3.4", "typescript": "~5.7.2", "vite": "^6.2.0"}}