import { defineConfig } from 'vite';

import { NovaTheme } from '@hxnova/themes';
import { extendTheme, pigment } from '@pigment-css/vite-plugin';
import react from '@vitejs/plugin-react';

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    pigment({
      theme: extendTheme(NovaTheme),
      transformLibraries: ['@hxnova/react-components'],
      babelOptions: {
        plugins: [`@babel/plugin-transform-export-namespace-from`],
      },
    }),
    react({
      babel: {
        plugins: ['@babel/plugin-transform-export-namespace-from'],
      },
    }),
  ],
  optimizeDeps: {
    include: ['prop-types', 'react-is', 'hoist-non-react-statics', '@mui/utils', '@base-ui-components/react'],
    exclude: ['@hxnova/themes', '@hxnova/react-components', '@hxnova/mi-react-components'],
  },
});
