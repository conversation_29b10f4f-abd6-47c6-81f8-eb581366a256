// ===== Constants =====
export const IGNORED_FOLDERS = ['internal', 'utils', 'test', '__tests__', 'Card'];

// Group components by their categories
export const GROUPED_COMPONENTS: Record<string, string> = {
  PlainCard: 'Cards',
  EmptyCard: 'Cards',
  ProductCard: 'Cards',

  FileStatus: 'Files',
  FileItem: 'Files',
  FilePanel: 'Files',
  FilePicker: 'Files',
};

export const IGNORED_PATTERNS = [/\.test\./, /\.spec\./, /\.stories\./];

// Component to folder mappings for special cases
export const COMPONENT_FOLDER_MAPPING: Record<string, string> = {};

// Add a mapping for special folder names to their storybook titles
export const FOLDER_TITLE_MAPPING: Record<string, string> = {};

export const COMPONENT_NAME_MAPPING: Record<string, string> = {};

// Map subcomponents to their main components
export const MAIN_COMPONENT_MAPPING: Record<string, string> = {
  FilePickerBlock: 'FilePicker',
  DropOverlay: 'FilePicker',
};
