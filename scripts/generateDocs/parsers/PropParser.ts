/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * PropParser - Parses component props using TypeScript compiler API
 */
import * as fs from 'fs';
import * as path from 'path';
import * as docgen from 'react-docgen-typescript';
import { PropDetail } from '../types';
import { getDeprecatedInfo } from '../utils/docCommentUtils';
import { formatTypeDescriptionFromDocgen } from '../utils/typeFormatters';

// DocgenPropItem interface with improved typing over the original implementation
interface DocgenPropItem {
  type: docgen.PropItemType;
  description: string;
  defaultValue?: {
    value: string;
    computed?: boolean;
  } | null;
  required: boolean;
  tags?: Record<string, any>;
}

/**
 * PropParser class for extracting and formatting component props
 */
export class PropParser {
  private docgenParser: docgen.FileParser;
  private tsConfigPath: string;

  /**
   * Creates a new PropParser
   * @param tsConfigPath Path to the tsconfig.json file
   */
  constructor(tsConfigPath?: string) {
    // Configure docgen parser options
    const parserOptions: docgen.ParserOptions = {
      propFilter: (prop: docgen.PropItem) => {
        // Skip all node_modules props
        if (prop.declarations && prop.declarations[0].fileName.includes('node_modules')) {
          return false;
        }
        return true;
      },
      shouldExtractLiteralValuesFromEnum: true,
      shouldExtractValuesFromUnion: true,
      shouldRemoveUndefinedFromOptional: true,
      savePropValueAsString: true,
    };

    // Initialize the parser with the config
    if (tsConfigPath && fs.existsSync(tsConfigPath)) {
      this.tsConfigPath = tsConfigPath;
      this.docgenParser = docgen.withCustomConfig(tsConfigPath, parserOptions);
    } else {
      // Fall back to default config if no tsconfig provided
      this.tsConfigPath = path.resolve(process.cwd(), 'apps/storybook/tsconfig.app.json');
      this.docgenParser = docgen.withCustomConfig(this.tsConfigPath, parserOptions);
    }
  }

  /**
   * Checks if a prop is required based on multiple indicators
   * @param propItem The prop item to check
   * @returns Boolean indicating if the prop is required
   */
  private isRequiredProp(propItem: DocgenPropItem): boolean {
    return propItem.required || (propItem.type?.raw && propItem.type.raw.includes('.isRequired')) || false;
  }

  /**
   * Processes function signature from JSDoc tags
   * @param tags The JSDoc tags object
   * @returns An object with signature, args, and return information
   */
  private processFunctionSignature(tags: Record<string, any>): {
    signature?: string;
    signatureArgs?: { name: string; description: string }[];
    signatureReturn?: { name: string; description: string };
  } {
    if (!tags) return {};

    // Extract param and return tags
    const paramTags = Object.entries(tags)
      .filter(([key]) => key.startsWith('param'))
      .map(([_, value]) => value);

    const returnTag = tags.returns || tags.return;

    if (paramTags.length === 0 && !returnTag) return {};

    // Build signature
    const params = paramTags
      .map((param: any) => {
        const name = param.name || 'arg';
        const type = param.type || 'any';
        const isOptional = param.optional || false;

        return isOptional ? `${name}?: ${type}` : `${name}: ${type}`;
      })
      .join(', ');

    const returnType = returnTag?.type || 'void';
    const signature = `function(${params}) => ${returnType}`;

    // Build signature args
    const signatureArgs = paramTags
      .filter((param: any) => param.description && param.name)
      .map((param: any) => ({
        name: param.name,
        description: param.description,
      }));

    // Build signature return
    let signatureReturn;
    if (returnTag?.description) {
      signatureReturn = {
        name: returnType,
        description: returnTag.description,
      };
    }

    return {
      signature,
      signatureArgs: signatureArgs.length > 0 ? signatureArgs : undefined,
      signatureReturn,
    };
  }

  /**
   * Merges provided props with global props (e.g. sx) that apply to all components.
   * Global props override any existing properties in the provided props.
   *
   * @param props Props object to merge
   * @returns  Merged props object with global props included
   */
  private mergeGlobalProps(props: Record<string, any>) {
    const globalProps: Record<string, DocgenPropItem> = {
      sx: {
        type: {
          name: 'SxProps',
          raw: 'SxProps',
        },
        required: false,
        description: 'The system prop that allows defining system overrides as well as additional CSS styles.',
      },
    };

    return { ...props, ...globalProps };
  }

  /**
   * Main function to parse component props from a file
   * @param filePath Path to the component file
   * @param componentName Name of the component to parse
   * @returns Array of PropDetail objects
   */
  public parseProps(filePath: string, componentName: string): PropDetail[] {
    const dir = path.dirname(filePath);
    // Common patterns for component files
    const componentPath = path.join(dir, `${componentName}.tsx`);
    console.log(`Parsing props from file: ${componentPath}`);

    try {
      if (!fs.existsSync(componentPath)) {
        console.error(`File does not exist: ${componentPath}`);
        return [];
      }

      const componentDocs = this.docgenParser.parse(componentPath);
      const componentDoc = componentDocs.find((doc) => doc.displayName === componentName);

      if (componentDoc?.props) {
        const mergedProps = this.mergeGlobalProps(componentDoc.props);

        // Convert filtered props to PropDetail array
        return this.convertToPropDetails(mergedProps);
      } else {
        console.error(`Error parsing props from ${filePath}:`);
      }
    } catch (error) {
      console.error(`Error parsing props from ${filePath}:`, error);
    }

    return [];
  }

  private convertToPropDetails(props: Record<string, any>): PropDetail[] {
    return Object.entries(props)
      .map(([propName, propItem]) => {
        const typedPropItem = propItem as DocgenPropItem;

        // Get type information
        const typeInfo = formatTypeDescriptionFromDocgen(typedPropItem.type);

        // Check if prop is required
        const requiredProp = this.isRequiredProp(typedPropItem);

        // Get deprecation info
        const { isDeprecated, info: deprecationInfo } = getDeprecatedInfo(typedPropItem.description || '');
        // Process function signature for callback props
        const functionSignature = this.processFunctionSignature(typedPropItem.tags || {});
        // Remove the deprecation notice from the description to avoid duplication
        let cleanDescription = typedPropItem.description || '';
        if (isDeprecated && deprecationInfo) {
          // Remove the @deprecated tag and its info from the description
          cleanDescription = cleanDescription.replace(/@deprecated\s*[^\n]*/, '').trim();
        }
        return {
          name: propName,
          type: typeInfo.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim(),
          description: cleanDescription,
          defaultValue: typedPropItem.defaultValue?.value.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim() || '',
          required: requiredProp,
          tags: typedPropItem.tags || {},
          isDeprecated,
          deprecationInfo,
          ...(functionSignature.signature && {
            signature: functionSignature.signature,
            signatureArgs: functionSignature.signatureArgs,
            signatureReturn: functionSignature.signatureReturn,
          }),
        };
      })
      .filter(Boolean)
      .sort((a, b) => {
        // Sort required props first, then alphabetically
        if (a.required && !b.required) return -1;
        if (!a.required && b.required) return 1;
        return a.name.localeCompare(b.name);
      });
  }
}
