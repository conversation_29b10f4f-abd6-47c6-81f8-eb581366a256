/**
 * InheritanceParser - Parses the component props inheritance using TypeScript Compiler API
 */
import * as fs from 'fs';
import * as path from 'path';
import * as ts from 'typescript';
import { createTypeScriptProject } from '../core/TypeScriptProject';
import { getNodeComments, parseJSDocComment } from '../utils/docCommentUtils';

/**
 * Class for extracting and formatting component props inheritance information.
 */
export class InheritanceParser {
  /**
   * Parses and returns a description of the props inheritance for a given component.
   * - Find the `<ComponentName>.types.ts` file.
   * - Look for the props type declaration (`I<ComponentName>`).
   * - Extract the JSDoc comment for the custom `@inheritPropsOf` tag.
   * - Format the inheritance description.
   *
   * @param filePath Path to the component file
   * @param componentName Name of the component
   * @returns String of the inheritance description or an empty string if not found
   */
  public parseInheritance(filePath: string, componentName: string): string {
    // Compose the path to the component's props type definition file.
    const componentDir = path.dirname(filePath);
    const typesFilePath = path.join(componentDir, `${componentName}.types.ts`);

    console.log(`Parsing inheritance from file: ${typesFilePath}`);

    if (!fs.existsSync(typesFilePath)) {
      console.warn(`Types file does not exist: ${typesFilePath}`);
      return '';
    }

    const project = createTypeScriptProject([typesFilePath]);
    const sourceFile = project.program.getSourceFile(typesFilePath);
    if (!sourceFile) return '';

    // Props type name convention: `I<ComponentName>` (e.g., IAppSwitcher).
    const propsTypeName = `I${componentName}`;

    // Traverse top-level statements to find the props type declaration.
    for (const statement of sourceFile.statements) {
      if (
        (ts.isTypeAliasDeclaration(statement) || ts.isInterfaceDeclaration(statement)) &&
        statement.name.text === propsTypeName
      ) {
        // Extract tags from the JSDoc comments
        const comments = getNodeComments(statement, sourceFile);
        const { tags } = parseJSDocComment(comments);

        if (typeof tags.inheritPropsOf === 'string') {
          // Compose the inheritance description.
          // Example: "The props of the Nova Popper are also available in `AppSwitcher`."
          return `The props of the ${tags.inheritPropsOf} are also available in \`${componentName}\`.`;
        } else {
          return '';
        }
      }
    }

    return '';
  }
}
