trigger:
  - main
  - next

pool:
  vmImage: ubuntu-latest

variables:
  IS_MAIN_BRANCH: $[eq(variables['Build.SourceBranch'], 'refs/heads/main')]
  IS_NEXT_BRANCH: $[eq(variables['Build.SourceBranch'], 'refs/heads/next')]
  pnpm_config_cache: $(Pipeline.Workspace)/.pnpm-store
  storybookEnvironment: 'dev'
  storybookTarget: 'nex-dev-mixnovastorybook-euw-stapp'
  BROWSERSLIST_IGNORE_OLD_DATA: true

stages:
  - stage: Build
    displayName: Build
    jobs:
      - job: Build
        steps:
          - checkout: self
            persistCredentials: true
            clean: true
            fetchDepth: 0

          - task: NodeTool@0
            inputs:
              versionSpec: "20.x"
            displayName: "Install Node.js"

          - task: npmAuthenticate@0
            inputs:
              workingFile: .npmrc

          - task: Cache@2
            inputs:
              key: 'pnpm | "$(Agent.OS)" | pnpm-lock.yaml'
              path: $(pnpm_config_cache)
            displayName: Cache pnpm

          - script: |
              corepack enable
              corepack prepare pnpm@9.14.2 --activate
              pnpm config set store-dir $(pnpm_config_cache)
            displayName: "Setup pnpm"

          - script: pnpm install --frozen-lockfile
            displayName: "Install Dependencies"
            timeoutInMinutes: 10
            
          - script: |
              export NODE_OPTIONS="--max-old-space-size=8192"
              npx nx run-many -t build
            displayName: "Build all Packages"
            timeoutInMinutes: 20

          - task: ArchiveFiles@2
            inputs:
              rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
              includeRootFolder: false
              archiveType: 'zip'
              archiveFile: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
              replaceExistingArchive: true

          - script: |
              ls
            displayName: Display working directory

          - task: PublishBuildArtifacts@1
            inputs:
              PathtoPublish: '$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip'
              ArtifactName: 'drop'
              publishLocation: 'container'

  - stage: Deploy
    dependsOn:
      - Build
    displayName: Deploy Storybook Static Site
    jobs:
      - job: Deploy
        steps:
          - download: current
            artifact: drop

          - task: ExtractFiles@1
            inputs:
              archiveFilePatterns: $(Pipeline.Workspace)/drop/*.zip
              destinationFolder: '$(System.DefaultWorkingDirectory)'

          - task: AzureCLI@2
            displayName: "Fetch Storybook Static Site API Key"
            inputs:
              azureSubscription: "Hexagon MI Nexus - ${{variables.storybookEnvironment}}"
              scriptType: bash
              scriptLocation: inlineScript
              inlineScript: |
                APIKEY=$(az staticwebapp secrets list --name "${{variables.storybookTarget}}" | jq -r '.properties.apiKey')
                echo "##vso[task.setvariable variable=storybookApiKey;issecret=true]$APIKEY"

          - script: ls
            displayName: Display working directory

          - script: tree $(System.DefaultWorkingDirectory)
            displayName: 'Show Sources'

          - task: AzureStaticWebApp@0
            displayName: Deploy Storybook Static Site
            inputs:
              workingDirectory: $(System.DefaultWorkingDirectory)/apps/storybook
              app_location: '/storybook-static'
              output_location: ''
              skip_app_build: true
              azure_static_web_apps_api_token: $(storybookApiKey)
            condition: succeeded()

  - stage: Analysis
    dependsOn:
      - Build
    displayName: Sonar Qube and Black Duck Analysis
    jobs:
      - job: Analysis
        steps:
          - template: /.azuredevops/templates/steps/black_duck_analyze.yml
            parameters:
              blackduckScan: true
              semVer: $(Build.BuildNumber)

          - template: /.azuredevops/templates/steps/sonar_qube_analyze.yml
            parameters:
              semVer: $(Build.BuildNumber)
              sonarSources: 'packages/mixnova-react-components/'
