import { ReactNode } from 'react';
import { ButtonPropsWithTestId } from '../types/NovaExtension';

export interface ICardActions {
  /**
   * Custom content to render in the `CardActions` panel. The priority is higher than `actionButtons` prop.
   */
  actions?: ReactNode;

  /**
   * Array of [Nova Button props](https://zeroheight.com/9a7698df1/p/64fa04-button-common/t/93e44d63e2)
   * or custom components to render in the `CardActions` panel
   */
  actionButtons?: ReadonlyArray<ButtonPropsWithTestId | ReactNode>;
}

export interface ICardLoading {
  /**
   * If `true`, a loading indicator will be displayed blocking interaction with the card
   * @default false
   * */
  loading?: boolean;

  /**
   * The style of loader
   * @default 'spinner'
   * */
  loadingMode?: 'spinner' | 'skeleton';
}
