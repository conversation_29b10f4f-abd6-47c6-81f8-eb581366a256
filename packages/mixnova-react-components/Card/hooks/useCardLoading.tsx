import { css } from '@pigment-css/react';
import { useMemo } from 'react';
import { LoadingPanel } from '../../LoadingPanel';

const loadingClass = css({
  position: 'relative',
  pointerEvents: 'none',
  userSelect: 'none',
});

const baseClass = css({
  position: 'relative',
});

export const useCardLoading = (loading?: boolean, loadingMode: 'skeleton' | 'spinner' = 'spinner') => {
  const renderLoadingPanel = useMemo(
    () => <LoadingPanel open={loading ?? false} sx={{ position: 'absolute', zIndex: 10 }} />,
    [loading],
  );

  return {
    cardLoadingClass: loading ? loadingClass : baseClass,
    renderLoadingPanel: loadingMode === 'spinner' ? renderLoadingPanel : null,
  };
};
