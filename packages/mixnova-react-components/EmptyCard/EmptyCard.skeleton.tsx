import { Skeleton } from '@hxnova/react-components/Skeleton';

export const SquareEmptyCardSkeleton = (
  <div
    sx={(theme) => ({
      padding: `${theme.vars.sys.viewport.spacing.padding.topBottom.md} ${theme.vars.sys.viewport.spacing.padding.leftRight.md}`,
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
    })}
  >
    <Skeleton variant="rounded" width={72} height={72} sx={{ marginBottom: 24 }} />
    <Skeleton variant="rounded" width="60%" height={24} sx={{ marginBottom: 8 }} />
    <Skeleton variant="rounded" width="80%" height={20} />
  </div>
);

export const RectangleEmptyCardSkeleton = (
  <div
    sx={(theme) => ({
      padding: `${theme.vars.sys.viewport.spacing.padding.topBottom.md} ${theme.vars.sys.viewport.spacing.padding.leftRight.md}`,
      flex: 1,
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
    })}
  >
    <div sx={{ flex: 1 }}>
      <Skeleton variant="rounded" width="30%" height={20} sx={{ marginBottom: 8 }} />
      <Skeleton variant="rounded" width="60%" height={18} />
    </div>
    <Skeleton variant="rounded" width={32} height={32} />
  </div>
);
