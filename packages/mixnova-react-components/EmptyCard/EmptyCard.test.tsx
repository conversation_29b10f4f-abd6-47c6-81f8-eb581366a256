import { skeletonClasses } from '@hxnova/react-components/Skeleton';
import '@testing-library/jest-dom/vitest';
import { cleanup, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { emptyStateClasses } from '../EmptyState';
import { loadingPanelClasses } from '../LoadingPanel';
import { EmptyCard } from './EmptyCard';
import emptyCardClasses from './EmptyCard.classes';
import { IEmptyCard } from './EmptyCard.types';

const mockClick = vi.fn();

afterEach(() => {
  cleanup();
});

const EmptyCardTemplate = (props: IEmptyCard) => {
  return <EmptyCard mode="square" header="TestHeader" description="TestDescription" onClick={mockClick} {...props} />;
};

describe('EmptyCard', () => {
  it('should correctly render square EmptyCard', async () => {
    const user = userEvent.setup();

    render(<EmptyCardTemplate />);
    expect(screen.getByTestId(emptyStateClasses.icon).textContent).toBe('add');
    expect(screen.getByTestId(emptyStateClasses.header).textContent).toBe('TestHeader');
    expect(screen.getByTestId(emptyCardClasses.description).textContent).toBe('TestDescription');

    await user.click(screen.getByTestId(emptyCardClasses.root));
    expect(mockClick).toHaveBeenCalled();
  });

  it('should correctly render rectangle EmptyCard', async () => {
    const user = userEvent.setup();

    const { container } = render(<EmptyCardTemplate mode="rectangle" />);
    expect(container.querySelector(`.material-symbols-outlined`)?.textContent).toBe('add');
    expect(screen.getByTestId(emptyCardClasses.rectangleHeader).textContent).toBe('TestHeader');
    expect(screen.getByTestId(emptyCardClasses.description).textContent).toBe('TestDescription');

    await user.click(screen.getByTestId(emptyCardClasses.root));
    expect(mockClick).toHaveBeenCalled();
  });

  it('should correctly render custom subcomponents', () => {
    render(
      <EmptyCardTemplate
        icon={<p>CustomIcon</p>}
        header={<p>CustomHeader</p>}
        description={<p>CustomDescription</p>}
      />,
    );
    expect(screen.getByText('CustomIcon')).toBeInTheDocument();
    expect(screen.getByText('CustomHeader')).toBeInTheDocument();
    expect(screen.getByText('CustomDescription')).toBeInTheDocument();
  });

  it('should correctly render loading state', async () => {
    const { rerender, container } = render(<EmptyCardTemplate loading loadingMode="spinner" />);
    expect(container.querySelector(`.${skeletonClasses.root}`)).not.toBeInTheDocument();
    expect(screen.getByTestId(loadingPanelClasses.root)).toBeInTheDocument();
    expect(screen.getByTestId(loadingPanelClasses.progress)).toBeInTheDocument();

    rerender(<EmptyCardTemplate loading loadingMode="skeleton" />);
    expect(container.querySelector(`.${skeletonClasses.root}`)).toBeInTheDocument();
    expect(screen.queryByTestId(loadingPanelClasses.root)).not.toBeInTheDocument();
    expect(screen.queryByTestId(loadingPanelClasses.progress)).not.toBeInTheDocument();
  });
});
