import { CardRoot } from '@hxnova/react-components/Card';
import { styled } from '@pigment-css/react';
import { EmptyState, emptyStateClasses } from '../EmptyState';

export const StyledCardRoot = styled(CardRoot)(({ theme }) => ({
  transition: 'background 300ms cubic-bezier(0.2, 0.0, 0, 1.0)',
  '&:hover': {
    backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
  },
  '&:focus-visible': {
    backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
  },
  '&:active': {
    backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
  },
}));

export const StyledEmptyState = styled(EmptyState)(({ theme }) => ({
  color: theme.vars.palette.primary,
  [`& .${emptyStateClasses.icon}`]: {
    color: theme.vars.palette.primary,
    fontSize: 72,
  },
}));

export const StyledRectangleContent = styled('div')(() => ({
  width: '100%',
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  textAlign: 'left',
}));
