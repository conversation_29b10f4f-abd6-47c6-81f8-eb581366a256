'use client';

import { Icon } from '@hxnova/icons';
import { ButtonBase } from '@hxnova/react-components/ButtonBase';
import { Typography } from '@hxnova/react-components/Typography';
import clsx from 'clsx';
import { forwardRef } from 'react';
import { CardLoadingManager } from '../Card/components/CardLoadingManager';
import { useCardLoading } from '../Card/hooks/useCardLoading';
import emptyCardClasses from './EmptyCard.classes';
import { RectangleEmptyCardSkeleton, SquareEmptyCardSkeleton } from './EmptyCard.skeleton';
import { StyledCardRoot, StyledEmptyState, StyledRectangleContent } from './EmptyCard.styled';
import { IEmptyCard } from './EmptyCard.types';

export const EmptyCard = forwardRef<HTMLDivElement, IEmptyCard>((props, ref) => {
  const {
    header = '',
    description = '',
    icon = <Icon family="material" name="add" />,
    mode = 'square',
    loading = false,
    loadingMode = 'spinner',
    className,
    ...restProps
  } = props;

  const { renderLoadingPanel, cardLoadingClass } = useCardLoading(loading, loadingMode);

  const renderRectangleHeader =
    typeof header === 'string' ? (
      <Typography
        className={emptyCardClasses.rectangleHeader}
        data-testid={emptyCardClasses.rectangleHeader}
        variant="bodyLarge"
        sx={(theme) => ({
          fontWeight: 400,
          color: theme.vars.palette.primary,
        })}
      >
        {header}
      </Typography>
    ) : (
      header
    );

  const renderDescription =
    typeof description === 'string' ? (
      <Typography
        className={emptyCardClasses.description}
        data-testid={emptyCardClasses.description}
        variant="labelMedium"
        sx={(theme) => ({
          color: theme.vars.palette.onSurfaceVariant,
          whiteSpace: 'normal',
        })}
      >
        {description}
      </Typography>
    ) : (
      description
    );

  return (
    <StyledCardRoot
      ref={ref}
      className={clsx(emptyCardClasses.root, cardLoadingClass, className)}
      data-testid={emptyCardClasses.root}
      {...restProps}
    >
      <CardLoadingManager
        loading={loading}
        loadingMode={loadingMode}
        skeleton={mode === 'square' ? SquareEmptyCardSkeleton : RectangleEmptyCardSkeleton}
      >
        <ButtonBase
          sx={(theme) => ({
            flex: 1,
            padding: `${theme.vars.sys.viewport.spacing.padding.topBottom.md} ${theme.vars.sys.viewport.spacing.padding.leftRight.md}`,
          })}
          disabled={loading}
        >
          {mode === 'square' ? (
            <StyledEmptyState icon={icon} header={header} description={renderDescription} />
          ) : (
            <StyledRectangleContent>
              <div
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                {renderRectangleHeader}
                {renderDescription}
              </div>
              <div
                sx={(theme) => ({
                  color: theme.vars.palette.primary,
                  fontSize: 32,
                })}
              >
                {icon}
              </div>
            </StyledRectangleContent>
          )}
        </ButtonBase>
        {renderLoadingPanel}
      </CardLoadingManager>
    </StyledCardRoot>
  );
});
