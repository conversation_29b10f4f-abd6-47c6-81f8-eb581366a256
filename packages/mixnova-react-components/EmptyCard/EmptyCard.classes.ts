import generateUtilityClasses from '@mui/utils/generateUtilityClasses';

export interface EmptyCardClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the header element when `mode='rectangle'`. Use `.MIxNovaEmptyState-header` to style the header when `mode='square'`. */
  rectangleHeader: string;
  /** Class name applied to the description element. */
  description: string;
}

const emptyCardClasses: EmptyCardClasses = generateUtilityClasses('MIxNovaEmptyCard', [
  'root',
  'rectangleHeader',
  'description',
]);

export default emptyCardClasses;
