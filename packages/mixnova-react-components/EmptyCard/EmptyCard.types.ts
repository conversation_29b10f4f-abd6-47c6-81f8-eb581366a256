import { CardRootProps } from '@hxnova/react-components/Card';
import { ReactNode } from 'react';
import { ICardLoading } from '../Card/Card.types';

/**
 * @inheritPropsOf Nova [Card.Root](https://zeroheight.com/9a7698df1/p/38e9e0-cards/b/65548f)
 */
export type IEmptyCard = Omit<CardRootProps, 'children'> &
  ICardLoading & {
    /**
     * The Icon element displayed on the top of Card. It could be an Icon or an image.
     * @default <Icon family="material" name="add" />
     */
    icon?: ReactNode;

    /**
     * The header of the component. It could be a string or a custom component.
     * @default ''
     */
    header?: ReactNode;

    /**
     * The description for additional information. It could be a string or a custom component.
     * @default ''
     */
    description?: ReactNode;

    /**
     * Determines the shape of the card
     * @default 'square'
     */
    mode?: 'square' | 'rectangle';
  };
