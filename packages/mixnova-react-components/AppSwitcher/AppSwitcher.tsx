'use client';

import { Icon } from '@hxnova/icons';
import { CircularProgress } from '@hxnova/react-components/CircularProgress';
import { ClickAwayListener } from '@hxnova/react-components/ClickAwayListener';
import { Grid } from '@hxnova/react-components/Grid';
import { Popper } from '@hxnova/react-components/Popper';
import { Tooltip } from '@hxnova/react-components/Tooltip';
import clsx from 'clsx';
import { forwardRef, useEffect } from 'react';
import { openLink } from '../utils/functions/openLink';
import { useTranslate } from '../utils/locales';
import appSwitcherClasses from './AppSwitcher.classes';
import {
  AppSwitcherContainer,
  ItemLink,
  ItemLogo,
  ItemLogoContainer,
  ItemPermanentIcon,
  ItemTitle,
  LoadingContainer,
  MoreButton,
} from './AppSwitcher.styled';
import { IAppItem, IAppSwitcher } from './AppSwitcher.types';
import NexusX from './NexusX';
import translations from './translations';

const AppItem = (props: IAppItem) => {
  const { name, target = '_blank', url, logoSrc, 'data-testid': testid } = props;

  const renderLogo = (
    <ItemLogoContainer>
      {logoSrc && typeof logoSrc === 'string' ? <ItemLogo src={logoSrc} loading="lazy" alt={name} /> : logoSrc}
    </ItemLogoContainer>
  );

  return (
    <Tooltip
      title={<div translate="no">{name}</div>}
      sx={{
        width: '100%',
      }}
    >
      <ItemLink
        underline="none"
        onClick={(e) => {
          e.preventDefault();
          openLink(url, target);
        }}
        href={url}
        rel="noopener noreferrer"
        target={target}
        data-testid={testid}
      >
        {renderLogo}
        <ItemTitle variant="labelSmall" translate="no">
          {name}
        </ItemTitle>
      </ItemLink>
    </Tooltip>
  );
};

export const AppSwitcher = forwardRef<HTMLDivElement, IAppSwitcher>((props, ref) => {
  const {
    solutions,
    moreProductsUrl,
    myProjectsUrl = 'https://nexus.hexagon.com/platform/',
    homeUrl = 'https://nexus.hexagon.com/home/',
    loading = false,
    className,
    onClose,
    open,
    style,
    ...restProps
  } = props;

  const t = useTranslate(translations);

  const handleClickAway = (event: MouseEvent | TouchEvent) => {
    onClose?.(event, 'backdropClick');
  };

  useEffect(() => {
    if (!open) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose?.(event, 'escapeKeyDown');
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [open, onClose]);

  // Default solutions to always show in AppSwitcher
  const permanentSolutions: IAppItem[] = [
    {
      name: t('Home'),
      logoSrc: (
        <ItemPermanentIcon>
          <NexusX height={22} />
        </ItemPermanentIcon>
      ),
      url: homeUrl,
    },
    {
      name: t('My Projects'),
      logoSrc: (
        <ItemPermanentIcon>
          <Icon family="material" name="folder" size={24} color="white" />
        </ItemPermanentIcon>
      ),
      url: myProjectsUrl,
    },
  ];

  return (
    <Popper ref={ref} placement="bottom-start" open={open} {...restProps}>
      <ClickAwayListener onClickAway={handleClickAway}>
        <AppSwitcherContainer
          className={clsx(appSwitcherClasses.root, className)}
          data-testid={appSwitcherClasses.root}
          style={style}
        >
          <Grid container spacing={4} role="menu" aria-label="App switcher">
            {permanentSolutions.concat(solutions).map((solution, index) => (
              <Grid
                key={index}
                size={4}
                className={appSwitcherClasses.appItem}
                data-testid={appSwitcherClasses.appItem}
                role="menuitem"
              >
                <AppItem {...solution} />
              </Grid>
            ))}
          </Grid>

          <MoreButton
            size="small"
            onClick={(e) => {
              e.preventDefault();
              openLink(moreProductsUrl);
            }}
            href={moreProductsUrl}
            rel="noopener noreferrer"
            variant="text"
            color="primary"
            endIcon={<Icon family="material" name="arrow_forward" />}
            className={appSwitcherClasses.more}
            data-testid={appSwitcherClasses.more}
            aria-label={t('More Products')}
          >
            {t('More Products')}
          </MoreButton>

          {loading && (
            <LoadingContainer className={appSwitcherClasses.loading} data-testid={appSwitcherClasses.loading}>
              <CircularProgress />
            </LoadingContainer>
          )}
        </AppSwitcherContainer>
      </ClickAwayListener>
    </Popper>
  );
});
