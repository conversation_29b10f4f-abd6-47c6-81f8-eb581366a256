import { typographyClasses } from '@hxnova/react-components/Typography';
import '@testing-library/jest-dom/vitest';
import { render, screen } from '@testing-library/react';
import userEvent, { UserEvent } from '@testing-library/user-event';
import { useState } from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import * as openLinkUtils from '../utils/functions/openLink';
import { AppSwitcher } from './AppSwitcher';
import appSwitcherClasses from './AppSwitcher.classes';
import { IAppSwitcher } from './AppSwitcher.types';

const mockOnClose = vi.fn();
vi.spyOn(openLinkUtils, 'openLink').mockImplementation(() => {});

const AppSwitcherTemplate = ({ solutions = [], ...restProps }: Partial<IAppSwitcher>) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose: IAppSwitcher['onClose'] = (_, reason) => {
    setAnchorEl(null);
    mockOnClose(_, reason);
  };

  return (
    <>
      <button onClick={handleClick} data-testid="appSwitcher-trigger">
        AppSwitcherTrigger
      </button>
      <AppSwitcher
        onClose={handleClose}
        open={open}
        moreProductsUrl="example.com/more-products"
        homeUrl="example.com/home"
        myProjectsUrl="example.com/my-projects"
        solutions={solutions}
        {...restProps}
      />
    </>
  );
};

describe('AppSwitcher', () => {
  let user: UserEvent;

  beforeEach(() => {
    user = userEvent.setup();
  });

  it('should correctly render AppSwitcher with default options', async () => {
    render(<AppSwitcherTemplate />);
    // AppSwitcher should not be rendered initially
    expect(screen.queryByTestId(appSwitcherClasses.root)).not.toBeInTheDocument();
    expect(screen.queryByTestId(appSwitcherClasses.more)).not.toBeInTheDocument();
    expect(screen.queryByTestId(appSwitcherClasses.appItem)).not.toBeInTheDocument();
    expect(screen.queryByTestId(appSwitcherClasses.loading)).not.toBeInTheDocument();

    // Open AppSwitcher
    await user.click(screen.getByTestId('appSwitcher-trigger'));
    expect(screen.getByTestId(appSwitcherClasses.root)).toBeInTheDocument();
    // 2 default options (Home and My Projects) must be present
    const items = screen.getAllByTestId(appSwitcherClasses.appItem);
    expect(items.length).toBe(2);
    const [homeItem, projectsItem] = items;
    expect(homeItem.querySelector(`.${typographyClasses.root}`)).toHaveTextContent('Home');
    expect(homeItem.querySelector('a')).toHaveAttribute('href', 'example.com/home');
    expect(projectsItem.querySelector(`.${typographyClasses.root}`)).toHaveTextContent('My Projects');
    expect(projectsItem.querySelector('a')).toHaveAttribute('href', 'example.com/my-projects');
  });

  it('should correctly render with additional solutions', async () => {
    const solutions: IAppSwitcher['solutions'] = [
      {
        name: 'With logoSrc as remote URL',
        logoSrc: 'example.com/logo.png',
        url: 'example.com/remote-logo',
        'data-testid': 'remote-logo-item',
        target: '_blank',
      },
      {
        name: 'With logoSrc as ReactElement',
        logoSrc: <div data-testid="custom-logo" />,
        url: 'example.com/custom-logo',
        'data-testid': 'custom-logo-item',
        target: '_self',
      },
      {
        name: 'Without logoSrc and target',
        url: 'example.com/no-logo',
        'data-testid': 'no-logo-item',
      },
    ];

    render(<AppSwitcherTemplate open solutions={solutions} />);
    expect(screen.getAllByTestId(appSwitcherClasses.appItem).length).toBe(5); // 2 default + 3 custom solutions

    const remoteLogoEl = screen.getByTestId('remote-logo-item');
    expect(remoteLogoEl.textContent).toBe('With logoSrc as remote URL');
    expect(remoteLogoEl.querySelector('img')).toHaveAttribute('src', 'example.com/logo.png');
    await user.click(remoteLogoEl);
    expect(openLinkUtils.openLink).toHaveBeenCalledWith('example.com/remote-logo', '_blank');

    const customLogoEl = screen.getByTestId('custom-logo-item');
    expect(screen.getByTestId('custom-logo')).toBeInTheDocument();
    await user.click(customLogoEl);
    expect(openLinkUtils.openLink).toHaveBeenCalledWith('example.com/custom-logo', '_self');

    const noLogoEl = screen.getByTestId('no-logo-item');
    expect(noLogoEl.querySelector('img')).toBeNull();
    await user.click(noLogoEl);
    expect(openLinkUtils.openLink).toHaveBeenCalledWith('example.com/no-logo', '_blank');
  });

  it('should correctly handle close action', async () => {
    render(<AppSwitcherTemplate />);

    await user.click(screen.getByTestId('appSwitcher-trigger'));
    expect(screen.getByTestId(appSwitcherClasses.root)).toBeInTheDocument();

    // Click outside
    await user.click(document.body);
    expect(screen.queryByTestId(appSwitcherClasses.root)).not.toBeInTheDocument();
    expect(mockOnClose).toHaveBeenCalledWith(expect.any(Event), 'backdropClick');

    mockOnClose.mockClear();

    // Press Escape key
    await user.click(screen.getByTestId('appSwitcher-trigger'));
    expect(screen.getByTestId(appSwitcherClasses.root)).toBeInTheDocument();
    await user.keyboard('{Escape}');
    expect(screen.queryByTestId(appSwitcherClasses.root)).not.toBeInTheDocument();
    expect(mockOnClose).toHaveBeenCalledWith(expect.any(Event), 'escapeKeyDown');
  });

  it('should correctly render More Products link', async () => {
    render(<AppSwitcherTemplate open />);
    await user.click(screen.getByTestId(appSwitcherClasses.more));
    expect(openLinkUtils.openLink).toHaveBeenCalledWith('example.com/more-products');
  });

  it('should correctly render loading state', async () => {
    render(<AppSwitcherTemplate open loading />);
    expect(screen.getByTestId(appSwitcherClasses.loading)).toBeInTheDocument();
  });
});
