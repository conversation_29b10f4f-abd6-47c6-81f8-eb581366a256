import { PopperProps } from '@hxnova/react-components/Popper';
import { ReactElement } from 'react';

export interface IAppItem {
  /** The text label displayed for the item. */
  name: string;

  /** Source location for the image to display. Can be a remote URL or a reference to an imported file or a ReactElement, like icon */
  logoSrc?: string | ReactElement;

  /** The url to link to when clicked. Can be an absolute, or relative URL. */
  url: string;

  /**
   * Specifies the context in which the linked resource will open.
   * @default '_blank'
   */
  target?: '_blank' | '_self';

  /**
   * Optional test id to apply to the item. This will be applied to the top-level Link component.
   */
  'data-testid'?: string;
}

/**
 * @inheritPropsOf Nova [Popper](https://mui.com/base-ui/react-popper/components-api/#popper)
 */
export type IAppSwitcher = Omit<PopperProps, 'children'> & {
  /**
   * The solution items. These will be concatenated with the 2 default app items (Home and My Projects).
   *
   * ```
   * interface IAppItem {
   *   name: string;
   *   logoSrc?: string ⏐ ReactElement;
   *   url: string;
   *   target?: '_blank' ⏐ '_self';
   *   'data-testid'?: string;
   * }
   * ```
   */
  solutions: ReadonlyArray<IAppItem>;

  /**
   * Override for the Home button link (e.g., to point to a different environment for testing).
   * @default https://nexus.hexagon.com/home/
   */
  homeUrl?: string;

  /**
   * Override for the My Projects button link (e.g., to point to a different environment for testing).
   * @default https://nexus.hexagon.com/platform/
   */
  myProjectsUrl?: string;

  /** The url for more products. Click to jump to more products page. */
  moreProductsUrl: string;

  /**
   * Callback fired when the component requests to be closed.
   * The `reason` parameter can optionally be used to control the response to `onClose`.
   * The `reason` value can be `'backdropClick'` or `'escapeKeyDown'`.
   */
  onClose?: (event: object, reason: 'backdropClick' | 'escapeKeyDown') => void;

  /**
   * If `true`, the loading circle will appear.
   * @default false
   */
  loading?: boolean;
};
