import { type SVGProps, memo } from 'react';

const Nexus = memo((props: SVGProps<SVGSVGElement>) => (
  <svg width="22" height="24" viewBox="0 0 22 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path
      d="M16.702 5.94335C16.9839 6.24018 17.1766 6.61941 17.2554 7.03289C17.3343 7.44637 17.2958 7.87544 17.1449 8.26562C16.994 8.6558 16.7374 8.98949 16.4078 9.22432C16.0782 9.45915 15.6903 9.58453 15.2936 9.58453C14.8968 9.58453 14.509 9.45915 14.1793 9.22432C13.8497 8.98949 13.5931 8.6558 13.4422 8.26562C13.2913 7.87544 13.2528 7.44637 13.3317 7.03289C13.4105 6.61941 13.6032 6.24018 13.8851 5.94335C14.26 5.54875 14.7661 5.32739 15.2936 5.32739C15.821 5.32739 16.3272 5.54875 16.702 5.94335Z"
      fill="white"
    />
    <path
      d="M8.11458 5.94335C8.39654 6.24018 8.58918 6.61941 8.66804 7.03289C8.7469 7.44637 8.70843 7.87544 8.5575 8.26562C8.40658 8.6558 8.15001 8.98949 7.82039 9.22432C7.49076 9.45915 7.10294 9.58453 6.70616 9.58453C6.30939 9.58453 5.92157 9.45915 5.59194 9.22432C5.26231 8.98949 5.00575 8.6558 4.85482 8.26562C4.7039 7.87544 4.66542 7.44637 4.74428 7.03289C4.82315 6.61941 5.01579 6.24018 5.29774 5.94335C5.67257 5.54875 6.17871 5.32739 6.70616 5.32739C7.23362 5.32739 7.73976 5.54875 8.11458 5.94335Z"
      fill="white"
    />
    <path
      d="M3.82199 1.36595C4.10395 1.66279 4.29659 2.04202 4.37545 2.4555C4.45431 2.86898 4.41584 3.29805 4.26492 3.68823C4.11399 4.07841 3.85742 4.4121 3.52779 4.64693C3.19816 4.88176 2.81034 5.00713 2.41357 5.00713C2.01679 5.00713 1.62897 4.88176 1.29934 4.64693C0.96971 4.4121 0.713142 4.07841 0.562218 3.68823C0.411294 3.29805 0.372822 2.86898 0.451683 2.4555C0.530545 2.04202 0.723185 1.66279 1.00514 1.36595C1.37997 0.971355 1.88611 0.75 2.41357 0.75C2.94102 0.75 3.44717 0.971355 3.82199 1.36595Z"
      fill="white"
    />
    <path
      d="M12.4101 10.5105C12.6916 10.8075 12.8839 11.1867 12.9624 11.6001C13.0409 12.0134 13.0022 12.4423 12.8512 12.8322C12.7001 13.2222 12.4436 13.5556 12.114 13.7902C11.7845 14.0249 11.3968 14.1501 11.0002 14.1501C10.6036 14.1501 10.216 14.0249 9.88642 13.7902C9.55688 13.5556 9.30031 13.2222 9.14927 12.8322C8.99823 12.4423 8.95952 12.0134 9.03805 11.6001C9.11659 11.1867 9.30882 10.8075 9.59038 10.5105C9.96532 10.1149 10.4721 9.89301 11.0002 9.89301C11.5284 9.89301 12.0351 10.1149 12.4101 10.5105Z"
      fill="#A5D867"
    />
    <path
      d="M20.9946 1.36595C21.2765 1.66279 21.4692 2.04202 21.548 2.4555C21.6269 2.86898 21.5884 3.29805 21.4375 3.68823C21.2866 4.07841 21.03 4.4121 20.7004 4.64693C20.3708 4.88176 19.9829 5.00713 19.5862 5.00713C19.1894 5.00713 18.8016 4.88176 18.4719 4.64693C18.1423 4.4121 17.8857 4.07841 17.7348 3.68823C17.5839 3.29805 17.5454 2.86898 17.6243 2.4555C17.7032 2.04202 17.8958 1.66279 18.1777 1.36595C18.5526 0.971355 19.0587 0.75 19.5862 0.75C20.1136 0.75 20.6198 0.971355 20.9946 1.36595Z"
      fill="white"
    />
    <path
      d="M5.29774 18.0684C5.01579 17.7715 4.82315 17.3923 4.74428 16.9788C4.66542 16.5653 4.7039 16.1363 4.85482 15.7461C5.00575 15.3559 5.26231 15.0222 5.59194 14.7874C5.92157 14.5526 6.30939 14.4272 6.70616 14.4272C7.10294 14.4272 7.49076 14.5526 7.82039 14.7874C8.15001 15.0222 8.40658 15.3559 8.5575 15.7461C8.70843 16.1363 8.7469 16.5653 8.66804 16.9788C8.58918 17.3923 8.39654 17.7715 8.11458 18.0684C7.73976 18.463 7.23362 18.6843 6.70616 18.6843C6.17871 18.6843 5.67257 18.463 5.29774 18.0684Z"
      fill="white"
    />
    <path
      d="M13.8851 18.0684C13.6032 17.7715 13.4105 17.3923 13.3317 16.9788C13.2528 16.5653 13.2913 16.1363 13.4422 15.7461C13.5931 15.3559 13.8497 15.0222 14.1793 14.7874C14.509 14.5526 14.8968 14.4272 15.2936 14.4272C15.6903 14.4272 16.0782 14.5526 16.4078 14.7874C16.7374 15.0222 16.994 15.3559 17.1449 15.7461C17.2958 16.1363 17.3343 16.5653 17.2554 16.9788C17.1766 17.3923 16.9839 17.7715 16.702 18.0684C16.3272 18.463 15.821 18.6843 15.2936 18.6843C14.7661 18.6843 14.26 18.463 13.8851 18.0684Z"
      fill="white"
    />
    <path
      d="M18.1777 22.634C17.8958 22.3371 17.7032 21.9579 17.6243 21.5444C17.5454 21.131 17.5839 20.7019 17.7348 20.3117C17.8857 19.9215 18.1423 19.5878 18.4719 19.353C18.8016 19.1182 19.1894 18.9928 19.5862 18.9928C19.9829 18.9928 20.3708 19.1182 20.7004 19.353C21.03 19.5878 21.2866 19.9215 21.4375 20.3117C21.5884 20.7019 21.6269 21.131 21.548 21.5444C21.4692 21.9579 21.2765 22.3371 20.9946 22.634C20.6198 23.0286 20.1136 23.2499 19.5862 23.2499C19.0587 23.2499 18.5526 23.0286 18.1777 22.634Z"
      fill="white"
    />
    <path
      d="M1.00514 22.634C0.723185 22.3371 0.530545 21.9579 0.451683 21.5444C0.372822 21.131 0.411294 20.7019 0.562218 20.3117C0.713142 19.9215 0.96971 19.5878 1.29934 19.353C1.62897 19.1182 2.01679 18.9928 2.41357 18.9928C2.81034 18.9928 3.19816 19.1182 3.52779 19.353C3.85742 19.5878 4.11399 19.9215 4.26492 20.3117C4.41584 20.7019 4.45431 21.131 4.37545 21.5444C4.29659 21.9579 4.10395 22.3371 3.82199 22.634C3.44717 23.0286 2.94102 23.2499 2.41357 23.2499C1.88611 23.2499 1.37997 23.0286 1.00514 22.634Z"
      fill="white"
    />
  </svg>
));
export default Nexus;
