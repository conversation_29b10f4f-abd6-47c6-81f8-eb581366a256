import { Button } from '@hxnova/react-components/Button';
import { Link } from '@hxnova/react-components/Link';
import { Typography } from '@hxnova/react-components/Typography';
import { styled } from '@pigment-css/react';

export const AppSwitcherContainer = styled.div(({ theme }) => ({
  position: 'relative',
  backgroundColor: theme.vars.palette.surfaceContainer,
  maxWidth: 308,
  maxHeight: 440,
  padding: `${theme.vars.sys.viewport.spacing.padding.topBottom.md} ${theme.vars.sys.viewport.spacing.padding.leftRight.lg}`,
  border: `1px solid ${theme.vars.palette.outlineVariant}`,
  borderRadius: theme.vars.sys.viewport.radius.xs,
  overflow: 'auto',
}));

export const ItemLink = styled(Link)(({ theme }) => ({
  minHeight: 76,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: theme.vars.sys.viewport.spacing.spaceBetween.vertical['2xs'],
  '&:hover': {
    [`& ${ItemLogoContainer}`]: {
      backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.hoverPrimary})`,
    },
  },
  '&:focus, &:active': {
    [`& ${ItemLogoContainer}`]: {
      backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.focusPrimary})`,
    },
  },
}));

export const ItemLogoContainer = styled.div(({ theme }) => ({
  display: 'flex',
  width: '3.5rem',
  height: '3.5rem',
  borderRadius: theme.vars.sys.viewport.radius['2xs'],
  justifyContent: 'center',
  alignItems: 'center',
}));

export const ItemPermanentIcon = styled.div(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  height: '2.5rem',
  width: '2.5rem',
  backgroundColor: 'black',
  borderRadius: theme.vars.sys.viewport.radius.sm,
}));

export const ItemLogo = styled.img(() => ({
  maxWidth: '2.5rem',
  maxHeight: '2.5rem',
  objectFit: 'contain',
}));

export const ItemTitle = styled(Typography)(({ theme }) => ({
  paddingInline: '0.375rem',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  display: '-webkit-box',
  textAlign: 'center',
  WebkitLineClamp: '2',
  WebkitBoxOrient: 'vertical',
  color: theme.vars.palette.onSurface,
}));

export const MoreButton = styled(Button)(({ theme }) => ({
  marginTop: theme.vars.sys.viewport.spacing.spaceBetween.vertical.lg,
  textDecoration: 'none',
  height: 'auto',
  whiteSpace: 'normal',
  textAlign: 'left',
}));

export const LoadingContainer = styled.div(({ theme }) => ({
  position: 'absolute',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  inset: 0,
  backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.surfaceContainer} 75%)`,
}));
