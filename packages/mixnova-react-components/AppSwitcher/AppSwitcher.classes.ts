import generateUtilityClasses from '@mui/utils/generateUtilityClasses';

export interface AppSwitcherClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the app item element. */
  appItem: string;
  /** Class name applied to the "more" button element at the bottom of the AppSwitcher. */
  more: string;
  /** Class name applied to the loading indicator container. */
  loading: string;
}

const appSwitcherClasses: AppSwitcherClasses = generateUtilityClasses('MIxNovaAppSwitcher', [
  'root',
  'appItem',
  'more',
  'loading',
]);

export default appSwitcherClasses;
