import generateUtilityClasses from '@mui/utils/generateUtilityClasses';

export interface NavBarClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the nav icon element. */
  navIcon: string;
  /** Class name applied to the app logo element. */
  appLogo: string;
  /** Class name applied to the page title wrapper element. */
  pageTitleWrapper: string;
  /** Class name applied to the page title element. */
  pageTitleString: string;
  /** Class name applied to the badge element. */
  badge: string;
}

const navBarClasses: NavBarClasses = generateUtilityClasses('MIxNovaNavBar', [
  'root',
  'navIcon',
  'appLogo',
  'pageTitleWrapper',
  'pageTitleString',
  'badge',
]);

export default navBarClasses;
