import '@testing-library/jest-dom/vitest';
import { render, screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { NavBar } from './NavBar';
import navBarClasses from './NavBar.classes';

describe('NavBar', () => {
  it('should correctly render NavBar element with optional props', () => {
    render(
      <NavBar
        pageTitle="StringPageTitle"
        navIcon={<p>TestNavIcon</p>}
        appLogo={<p>TestAppLogo</p>}
        badge={<p>TestBadge</p>}
        leftActions={<p data-testid="left-actions">TestLeftActions</p>}
      />,
    );
    expect(screen.getByTestId(navBarClasses.root)).toBeInTheDocument();
    expect(screen.getByTestId(navBarClasses.navIcon).textContent).toBe('TestNavIcon');
    expect(screen.getByTestId(navBarClasses.appLogo).textContent).toBe('TestAppLogo');
    expect(screen.getByTestId(navBarClasses.badge).textContent).toBe('TestBadge');
    expect(screen.getByTestId(navBarClasses.pageTitleWrapper).textContent).toBe('StringPageTitle');
    expect(screen.getByTestId(navBarClasses.pageTitleString).textContent).toBe('StringPageTitle');
    expect(screen.getByTestId('left-actions').textContent).toBe('TestLeftActions');
  });

  it('should correctly render custom pageTitle', () => {
    render(<NavBar pageTitle={<p>CustomPageTitle</p>} />);
    expect(screen.getByTestId(navBarClasses.pageTitleWrapper).textContent).toBe('CustomPageTitle');
  });
});
