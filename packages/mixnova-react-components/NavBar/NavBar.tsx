'use client';

import { Typography } from '@hxnova/react-components/Typography';
import clsx from 'clsx';
import { forwardRef } from 'react';
import navBarClasses from './NavBar.classes';
import { INavBar } from './NavBar.types';

export const NavBar = forwardRef<HTMLDivElement, INavBar>((props, ref) => {
  const { navIcon, appLogo, badge, pageTitle, leftActions, children, className, ...restProps } = props;

  const renderNavIcon = navIcon && (
    <div
      className={navBarClasses.navIcon}
      data-testid={navBarClasses.navIcon}
      sx={{
        marginRight: 24,
      }}
    >
      {navIcon}
    </div>
  );

  const renderAppLogo = appLogo && (
    <div
      className={navBarClasses.appLogo}
      data-testid={navBarClasses.appLogo}
      sx={{
        marginRight: 24,
      }}
    >
      {appLogo}
    </div>
  );

  const renderPageTitle = pageTitle && (
    <div
      className={navBarClasses.pageTitleWrapper}
      data-testid={navBarClasses.pageTitleWrapper}
      sx={{
        marginRight: 24,
      }}
    >
      {typeof pageTitle === 'string' ? (
        <Typography
          className={navBarClasses.pageTitleString}
          data-testid={navBarClasses.pageTitleString}
          variant="titleSmall"
          sx={{
            fontWeight: 400,
          }}
        >
          {pageTitle}
        </Typography>
      ) : (
        pageTitle
      )}
    </div>
  );

  const renderBadge = badge && (
    <div
      className={navBarClasses.badge}
      data-testid={navBarClasses.badge}
      sx={{
        marginRight: 24,
      }}
    >
      {badge}
    </div>
  );

  return (
    <div
      className={clsx(navBarClasses.root, className)}
      data-testid={navBarClasses.root}
      ref={ref}
      sx={(theme) => ({
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        padding: `${theme.vars.sys.viewport.spacing.padding.topBottom.xs} ${theme.vars.sys.viewport.spacing.padding.leftRight.lg}`,
        backgroundColor: theme.vars.palette.surfaceContainer,
        minHeight: 48,
      })}
      {...restProps}
    >
      {renderNavIcon}
      {renderAppLogo}
      {renderPageTitle}
      {renderBadge}
      {leftActions}
      <div sx={(theme) => ({ flex: 1, marginRight: theme.vars.sys.viewport.spacing.spaceBetween.horizontal.xl })} />
      {children}
    </div>
  );
});
