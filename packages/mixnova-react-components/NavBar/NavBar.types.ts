import { HTMLAttributes, ReactNode } from 'react';

export type INavBar = HTMLAttributes<HTMLDivElement> & {
  /**
   *  Leftmost icon in the toolbar — typically a menu, back arrow, or close icon.
   */
  navIcon?: ReactNode;

  /**
   *  Graphic to display representing the application.
   */
  appLogo?: ReactNode;

  /**
   *  Optional status badge to display next to the title / logo.
   */
  badge?: ReactNode;

  /**
   *  Text title to display for the page. It could be a string or a custom component.
   */
  pageTitle?: ReactNode;

  /**
   *  Custom content to render on the left side of the toolbar after the (optional) title.
   */
  leftActions?: ReactNode;
};
