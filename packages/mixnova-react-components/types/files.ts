import type { FileRejection, FileWithPath } from 'react-dropzone';

export type { FileRejection, FileWithPath };

/**
 * File upload status
 */
export enum UploadStatus {
  /**
   * File is uploading
   */
  Uploading = 'uploading',
  /**
   * File upload failed
   */
  Failed = 'failed',
  /**
   * File upload succeeded
   */
  Succeeded = 'succeeded',
}

export interface FileWithStatus {
  file: FileWithPath;
  status: UploadStatus;
  progress?: number;
}
