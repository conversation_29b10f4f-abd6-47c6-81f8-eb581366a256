import { IconButtonProps } from '@hxnova/react-components/IconButton';
import { TooltipProps } from '@hxnova/react-components/Tooltip';
import { HTMLAttributes, MouseEventHandler, ReactNode } from 'react';

export type ActionItem = {
  label: string;
  icon: ReactNode;
  inactiveIcon?: ReactNode;
  active?: boolean;
  onClick: MouseEventHandler<HTMLElement>;
  'data-testid'?: string;
  className?: string;
};

export type IActionGroup = Omit<HTMLAttributes<HTMLDivElement>, 'children'> & {
  /**
   *  List of action icons to include. Each action can be configured as a toggle to switch between icons based on active/inactive state.
   *
   * ```
   * type ActionItem = {
   *   label: string;
   *   icon: ReactElement;
   *   inactiveIcon?: ReactElement;
   *   active?: boolean;
   *   onClick: () => void;
   * }
   * ```
   * @default []
   */
  actions: ReadonlyArray<ActionItem>;

  /**
   *  Maximum number of icons to display. If the number of actions exceeds this number, the overflow will be displayed in a dropdown menu.
   *
   * @default 3
   */
  max?: number;

  /**
   * Size for the IconButton elements.
   *
   * @default 'medium'
   */
  size?: IconButtonProps['size'];

  /**
   * Props to pass through to the IconButton [Tooltip](https://zeroheight.com/9a7698df1/p/05eaf0-tooltip/b/916910) element.
   *
   * @default {}
   */
  TooltipProps?: Partial<TooltipProps>;

  /**
   * Props to pass through to the more action IconButton [Tooltip](https://zeroheight.com/9a7698df1/p/05eaf0-tooltip/b/916910) element.
   *
   * @default {}
   */
  moreActionsTooltipProps?: Partial<TooltipProps>;
};
