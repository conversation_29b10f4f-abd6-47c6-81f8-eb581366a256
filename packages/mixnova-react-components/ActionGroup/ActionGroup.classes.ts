import generateUtilityClasses from '@mui/utils/generateUtilityClasses';

export interface ActionGroupClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the active icon button element. */
  iconButtonActive: string;
  /** Class name applied to the inactive icon button element. */
  iconButtonInactive: string;
  /** Class name applied to the icon tooltip element. */
  iconTooltip: string;
  /** Class name applied to the overflow icon button element. */
  overflowIconButton: string;
  /** Class name applied to the menu element. */
  menu: string;
  /** Class name applied to the icon menu item element. */
  iconMenuItem: string;
}

const actionGroupClasses: ActionGroupClasses = generateUtilityClasses('MixNovaActionGroup', [
  'root',
  'iconButtonActive',
  'iconButtonInactive',
  'iconTooltip',
  'overflowIconButton',
  'menu',
  'iconMenuItem',
]);

export default actionGroupClasses;
