'use client';

import { Icon } from '@hxnova/icons';
import { IconButton } from '@hxnova/react-components/IconButton';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Menu } from '@hxnova/react-components/Menu';
import { MenuItem } from '@hxnova/react-components/MenuItem';
import { Tooltip } from '@hxnova/react-components/Tooltip';
import clsx from 'clsx';
import uniqueId from 'lodash/uniqueId';
import { forwardRef, useState } from 'react';
import { useTranslate } from '../utils/locales';
import actionGroupClasses from './ActionGroup.classes';
import { IActionGroup } from './ActionGroup.types';
import translations from './translations';

export const ActionGroup = forwardRef<HTMLDivElement, IActionGroup>((props, ref) => {
  const {
    actions = [],
    max = 3,
    size = 'medium',
    TooltipProps = {},
    moreActionsTooltipProps = {},
    className,
    ...restProps
  } = props;

  const t = useTranslate(translations);

  const [componentUniqueId] = useState(() => uniqueId(`MIxNovaActionGroup-`));
  const cappedMax = Math.max(max, 1);
  const showOverflow = actions.length > cappedMax;

  const [menuAnchorEl, setMenuAnchorEl] = useState<HTMLElement | null>(null);
  const openMenu = Boolean(menuAnchorEl);

  const handleOverflowClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setMenuAnchorEl(e.currentTarget);
  };
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const renderVisibleActions = actions.slice(0, showOverflow ? cappedMax - 1 : cappedMax).map((action, index) => {
    const { active = true, icon, inactiveIcon = icon, onClick, ...restActionProps } = action;

    const actionClassName = active ? actionGroupClasses.iconButtonActive : actionGroupClasses.iconButtonInactive;

    return (
      <Tooltip
        key={index}
        title={action.label}
        className={actionGroupClasses.iconTooltip}
        data-testid={actionGroupClasses.iconTooltip}
        {...TooltipProps}
      >
        <IconButton
          variant="neutral"
          aria-label={action.label}
          size={size}
          onClick={(e) => {
            onClick(e);
            e.stopPropagation();
          }}
          className={actionClassName}
          data-testid={actionClassName}
          {...restActionProps}
        >
          {active ? icon : inactiveIcon}
        </IconButton>
      </Tooltip>
    );
  });

  const renderOverflow = showOverflow && (
    <div
      sx={{
        display: 'flex',
      }}
    >
      <Tooltip
        title={t('Show More Actions')}
        className={actionGroupClasses.iconTooltip}
        data-testid={actionGroupClasses.iconTooltip}
        {...TooltipProps}
        {...moreActionsTooltipProps}
      >
        <IconButton
          id={componentUniqueId}
          aria-label={t('Show More Actions')}
          variant="neutral"
          size={size}
          onClick={handleOverflowClick}
          className={actionGroupClasses.overflowIconButton}
          data-testid={actionGroupClasses.overflowIconButton}
        >
          <Icon family="material" name="more_horiz" size={20} />
        </IconButton>
      </Tooltip>

      <Menu
        className={actionGroupClasses.menu}
        data-testid={actionGroupClasses.menu}
        anchorEl={menuAnchorEl}
        open={openMenu}
        onClose={handleMenuClose}
        placement="right-start"
        slotProps={{
          listbox: {
            'aria-labelledby': componentUniqueId,
          },
        }}
      >
        {actions.slice(cappedMax - 1).map((action, index) => {
          const { label, icon, inactiveIcon = icon, active = true, onClick, ...restActionProps } = action;

          return (
            <MenuItem
              key={index}
              onClick={(e) => {
                onClick(e);
                e.stopPropagation();
                handleMenuClose();
              }}
              className={actionGroupClasses.iconMenuItem}
              data-testid={actionGroupClasses.iconMenuItem}
              {...restActionProps}
            >
              <ListItemDecorator
                sx={{
                  fontSize: 24,
                }}
              >
                {active ? icon : inactiveIcon}
              </ListItemDecorator>
              {label}
            </MenuItem>
          );
        })}
      </Menu>
    </div>
  );

  return (
    <div
      role="group"
      aria-label="Action Group"
      sx={(theme) => ({
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        gap: theme.vars.sys.viewport.spacing.spaceBetween.horizontal.xs,
      })}
      className={clsx(actionGroupClasses.root, className)}
      data-testid={actionGroupClasses.root}
      ref={ref}
      {...restProps}
    >
      {renderVisibleActions}
      {renderOverflow}
    </div>
  );
});
