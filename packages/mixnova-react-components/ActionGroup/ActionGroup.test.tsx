import '@testing-library/jest-dom/vitest';
import { cleanup, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { ActionGroup } from './ActionGroup';
import actionGroupClasses from './ActionGroup.classes';
import { IActionGroup } from './ActionGroup.types';

afterEach(() => {
  cleanup();
});

const mockDashboardClick = vi.fn();

const overflowActions: IActionGroup['actions'] = [
  {
    label: 'TestAction1',
    icon: <p>TestIcon1</p>,
    onClick: () => {},
  },
  {
    label: 'TestAction2',
    icon: <p>TestIcon2</p>,
    onClick: () => {},
  },
  {
    label: 'TestAction3',
    icon: <p>TestIcon3</p>,
    onClick: () => {},
  },
  {
    label: 'TestAction4',
    icon: <p>TestIcon4</p>,
    onClick: () => {},
  },
  {
    label: 'TestAction5',
    icon: <p>TestIcon5</p>,
    onClick: () => {},
  },
  {
    label: 'Dashboard',
    icon: <p>DashboardIcon</p>,
    onClick: mockDashboardClick,
  },
];

const noOverflowActions: IActionGroup['actions'] = [
  {
    label: 'TestAction1',
    icon: <p>TestIcon1</p>,
    onClick: () => {},
  },
  {
    label: 'TestAction2',
    icon: <p>TestIcon2</p>,
    active: false,
    onClick: () => {},
  },
];

describe('ActionGroup', () => {
  it('should correctly render ActionGroup element without overflow', () => {
    render(<ActionGroup actions={noOverflowActions} />);
    expect(screen.getByTestId(actionGroupClasses.root)).toBeInTheDocument();

    expect(screen.getAllByTestId(actionGroupClasses.iconButtonActive)).toHaveLength(1);
    expect(screen.getAllByTestId(actionGroupClasses.iconButtonInactive)).toHaveLength(1);
    expect(screen.getAllByTestId(actionGroupClasses.iconTooltip)).toHaveLength(2);

    expect(screen.queryByTestId(actionGroupClasses.overflowIconButton)).not.toBeInTheDocument();
    expect(screen.queryByTestId(actionGroupClasses.menu)).not.toBeInTheDocument();
    expect(screen.queryByTestId(actionGroupClasses.iconMenuItem)).not.toBeInTheDocument();
  });

  it('should correctly render ActionGroup element with overflow', async () => {
    const user = userEvent.setup();
    render(<ActionGroup actions={overflowActions} />);

    const overflowButton = screen.getByTestId(actionGroupClasses.overflowIconButton);
    expect(overflowButton).toBeInTheDocument();

    await user.click(overflowButton);
    expect(screen.getByTestId(actionGroupClasses.menu)).toBeInTheDocument();
    expect(screen.getAllByTestId(actionGroupClasses.iconMenuItem)).toHaveLength(4);

    await user.click(screen.getByText('DashboardIcon'));
    expect(mockDashboardClick).toHaveBeenCalled();
    expect(screen.queryByTestId(actionGroupClasses.menu)).not.toBeInTheDocument();
  });

  it('should correctly render actions with custom max', async () => {
    const user = userEvent.setup();

    // Mid-range
    const { rerender } = render(<ActionGroup actions={overflowActions} max={4} />);
    const overflowButton = screen.getByTestId(actionGroupClasses.overflowIconButton);
    expect(screen.getAllByTestId(actionGroupClasses.iconButtonActive)).toHaveLength(3);
    await user.click(overflowButton);
    expect(screen.getAllByTestId(actionGroupClasses.iconMenuItem)).toHaveLength(3);

    // Edge-case
    rerender(<ActionGroup actions={overflowActions} max={5} />);
    expect(screen.getAllByTestId(actionGroupClasses.iconButtonActive)).toHaveLength(4);
    await user.click(overflowButton);
    expect(screen.getAllByTestId(actionGroupClasses.iconMenuItem)).toHaveLength(2);

    // Absolute (no overflow)
    rerender(<ActionGroup actions={overflowActions} max={6} />);
    expect(screen.getAllByTestId(actionGroupClasses.iconButtonActive)).toHaveLength(6);

    // Invalid
    rerender(<ActionGroup actions={overflowActions} max={-10} />);
    expect(screen.queryByTestId(actionGroupClasses.iconButtonActive)).not.toBeInTheDocument();
    await user.click(overflowButton);
    expect(screen.getAllByTestId(actionGroupClasses.iconMenuItem)).toHaveLength(6);
  });
});
