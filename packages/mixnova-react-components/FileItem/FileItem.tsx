'use client';

import { ListItemButton } from '@hxnova/react-components/ListItemButton';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Tooltip } from '@hxnova/react-components/Tooltip';
import { Typography } from '@hxnova/react-components/Typography';
import clsx from 'clsx';
import { forwardRef } from 'react';
import { FileStatus } from '../FileStatus';
import { UploadStatus } from '../types';
import { getFileSize } from '../utils/functions/getFileSize';
import fileItemClasses from './FileItem.classes';
import { StyledFileItemContainer } from './FileItem.styled';
import { IFileItem } from './FileItem.types';

export const FileItem = forwardRef<HTMLDivElement, IFileItem>((props, ref) => {
  const {
    icon,
    name,
    size,
    hideSize = false,
    status = UploadStatus.Succeeded,
    errorMessage,
    progress = 0,
    actions,
    onRetry,
    onCancel,
    secondaryAction,
    className,
    ...restProps
  } = props;

  const fileSize = getFileSize(size);
  const isSucceeded = status === UploadStatus.Succeeded;

  const renderSecondaryAction = secondaryAction || (
    <div
      sx={(theme) => ({
        display: 'flex',
        alignItems: 'center',
        marginLeft: theme.vars.sys.viewport.spacing.spaceBetween.horizontal.xs,
      })}
    >
      {!hideSize && isSucceeded && (
        <Typography
          className={fileItemClasses.fileSize}
          data-testid={fileItemClasses.fileSize}
          variant="labelSmall"
          sx={(theme) => ({
            color: theme.vars.palette.onSurfaceVariant,
            marginRight: theme.vars.sys.viewport.spacing.spaceBetween.horizontal.xs,
          })}
        >
          {fileSize}
        </Typography>
      )}
      <FileStatus
        actions={actions}
        progress={progress}
        status={status}
        onRetry={onRetry}
        errorMessage={errorMessage}
        onCancel={onCancel}
      />
    </div>
  );

  return (
    <StyledFileItemContainer
      className={clsx(fileItemClasses.root, className)}
      data-testid={fileItemClasses.root}
      ref={ref}
      {...restProps}
    >
      <ListItemButton>
        <ListItemDecorator
          sx={{
            fontSize: 24,
          }}
        >
          {icon}
        </ListItemDecorator>
        <ListItemContent
          slotProps={{
            primary: {
              variant: 'labelSmall',
            },
          }}
          primary={
            <Tooltip
              className={fileItemClasses.fileName}
              data-testid={fileItemClasses.fileName}
              title={<div translate="no">{name}</div>}
              sx={{
                display: 'block',
                textOverflow: 'ellipsis',
                overflow: 'hidden',
              }}
            >
              {name}
            </Tooltip>
          }
        />
        {renderSecondaryAction}
      </ListItemButton>
    </StyledFileItemContainer>
  );
});
