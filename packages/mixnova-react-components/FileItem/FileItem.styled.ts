import { ListItem } from '@hxnova/react-components/ListItem';
import { styled } from '@pigment-css/react';
import { fileStatusClasses } from '../FileStatus';

export const StyledFileItemContainer = styled(ListItem)(({ theme }) => ({
  '--nova-listItem-gap': theme.vars.sys.viewport.spacing.spaceBetween.horizontal.xs,
  [`& .${fileStatusClasses['actions-succeeded']}`]: {
    visibility: 'hidden',
  },
  [`&:hover .${fileStatusClasses['actions-succeeded']}`]: {
    visibility: 'visible',
    opacity: 1,
  },
}));
