import { ListItemProps } from '@hxnova/react-components/ListItem';
import { ReactNode } from 'react';
import { IFileStatus } from '../FileStatus';

/**
 * @inheritPropsOf Nova [ListItem](https://zeroheight.com/9a7698df1/p/231da9-lists)
 */
export type IFileItem = Omit<ListItemProps, 'children'> &
  IFileStatus & {
    /**
     * File icon displayed at the start of the FileItem.
     */
    icon: ReactNode;

    /**
     * Name of the file.
     */
    name: string;

    /**
     * File size in bytes.
     */
    size: number;

    /**
     *  If `true`, the size of the file will not be displayed when `status='succeeded'`.
     *  @default false
     */
    hideSize?: boolean;

    /**
     * Element to render at the end of the FileItem.
     * If not provided, a default component will be rendered showing the file size (when `status='succeeded'`) and `FileStatus` component.
     */
    secondaryAction?: ReactNode;
  };
