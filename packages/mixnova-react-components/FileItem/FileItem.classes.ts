import generateUtilityClasses from '@mui/utils/generateUtilityClasses';

export interface FileItemClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the file name element. */
  fileName: string;
  /** Class name applied to the file size element. */
  fileSize: string;
}

const fileItemClasses: FileItemClasses = generateUtilityClasses('MIxNovaFileItem', ['root', 'fileName', 'fileSize']);

export default fileItemClasses;
