import '@testing-library/jest-dom/vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, expect, it, vi } from 'vitest';
import { fileStatusClasses } from '../FileStatus';
import { FileItem } from './FileItem';
import fileItemClasses from './FileItem.classes';
import { IFileItem } from './FileItem.types';

const mockOnCancel = vi.fn();

const FileItemTemplate = (props: Partial<IFileItem>) => {
  return <FileItem icon={<p>TestIcon</p>} name="TestFile.dxf" size={1234} onCancel={mockOnCancel} {...props} />;
};

describe('FileItem', () => {
  it('should render FileItem with icon, fileName, fileSize and FileStatus', () => {
    render(<FileItemTemplate />);
    expect(screen.getByTestId(fileItemClasses.root)).toBeInTheDocument();
    expect(screen.getByText('TestIcon')).toBeInTheDocument();
    expect(screen.getByTestId(fileItemClasses.fileName).textContent).toBe('TestFile.dxf');
    expect(screen.getByTestId(fileItemClasses.fileSize).textContent).toBe('1.2KB');
    expect(screen.getByTestId(fileStatusClasses.root)).toBeInTheDocument();
  });

  it('should call onCancel when cancel button is clicked', async () => {
    const user = userEvent.setup();

    render(<FileItemTemplate />);
    await user.click(
      screen.getByRole('button', {
        name: 'Cancel Action',
      }),
    );
    expect(mockOnCancel).toHaveBeenCalledTimes(1);
  });

  it('should hide file size when hideSize is true', () => {
    render(<FileItemTemplate hideSize />);
    expect(screen.queryByTestId(fileItemClasses.fileSize)).not.toBeInTheDocument();
  });

  it('should render custom secondary action', () => {
    render(<FileItemTemplate secondaryAction={<div>CustomSecondaryAction</div>} />);
    expect(screen.queryByTestId(fileStatusClasses.root)).not.toBeInTheDocument();
    expect(screen.getByText('CustomSecondaryAction')).toBeInTheDocument();
  });
});
