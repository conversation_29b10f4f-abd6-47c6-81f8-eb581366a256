import type { NovaPigmentTheme, SxProps } from '@hxnova/themes';
import type { ExtendTheme } from '@pigment-css/react/theme';

declare module '@pigment-css/react/theme' {
  interface ThemeTokens extends NovaPigmentTheme {}

  interface ThemeArgs {
    theme: ExtendTheme<{
      colorScheme: 'light' | 'dark';
      tokens: ThemeTokens;
    }>;
  }
}

declare global {
  interface Window {
    hxgnapi: any;
  }

  namespace React {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    interface HTMLAttributes<T> {
      sx?: SxProps;
      ownerState?: any;
    }
  }
}

// TODO: Fix Nova TooltipProps type and remove this augmentation
declare module '@hxnova/react-components/Tooltip' {
  interface TooltipProps {
    sx?: SxProps;
  }
}
