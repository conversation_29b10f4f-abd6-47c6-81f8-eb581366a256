import { HTMLAttributes, ReactElement } from 'react';
import { FileRejection, FileWithPath, FileWithStatus } from '../types';

export type IFilePanel = Omit<HTMLAttributes<HTMLDivElement>, 'children'> & {
  /**
   * Title of FilePanel displayed above the list of uploaded files.
   *  @default 'Uploaded Files'
   */
  label?: string;

  /**
   *  If `true`, the size of the files will not be displayed.
   *  @default false
   */
  hideSizes?: boolean;

  /**
   * Rejected files that selected from your local computer.
   *
   * ```
   *  export interface FileRejection {
   *    file: FileWithPath;
   *    errors: FileError[];
   *  }
   *
   * ```
   */
  rejectedFiles: ReadonlyArray<FileRejection>;

  /**
   * Function to generate error messages for rejected files.
   *
   * @example (rejection) => `${rejection.file.path || rejection.file.name}: (${rejection.errors[0].code}) ${rejection.errors[0].message}`
   */
  getErrorMessage?: (rejection: FileRejection) => string;

  /**
   * Accepted files that selected from your local computer.
   */
  acceptedFiles: ReadonlyArray<FileWithPath>;

  /**
   * Uploading files that selected from your local computer.
   *
   * ```
   * interface FileWithStatus {
   *  file: FileWithPath;
   *  status: UploadStatus;
   *  progress?: number;
   * }
   * ```
   *
   */
  uploadingFiles?: ReadonlyArray<FileWithStatus>;

  /**
   *  Set which file (e.g. "pdf") extension should be used as the parent for nesting files with the same base name.
   *
   *  - If `undefined`, or no file with the specified extension is found, files will nest under the first file alphabetically
   *  - If `null`, all nesting will be disabled.
   */
  parentFileExtension?: string | null;

  /**
   * Callback fired when a rejected error alert is closed.
   */
  onClose: (file: FileRejection) => void;

  /**
   * Callback fired when the delete button is clicked.
   */
  onRemove: (file: FileWithPath) => void;

  /**
   * Callback fired when the retry button is clicked for a file that failed to upload.
   */
  onRetry?: (file: FileWithPath) => void;

  /**
   * Custom mapping of file extensions to icons
   *
   * Example:
   *
   * ```
   * {
   *   mp3: <Icon family="material" name="audio_file" />,
   *   png: <Icon family="material" name="file_png" />
   * }
   * ```
   *
   * @default {}
   */
  iconMapping?: { [extension: string]: ReactElement };
};
