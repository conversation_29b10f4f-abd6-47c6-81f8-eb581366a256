import { Icon } from '@hxnova/icons';
import '@testing-library/jest-dom/vitest';
import { render, screen, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, expect, it, vi } from 'vitest';
import { fileItemClasses } from '../FileItem';
import { fileStatusClasses } from '../FileStatus';
import { FileWithStatus, UploadStatus } from '../types';
import { FilePanel } from './FilePanel';
import filePanelClasses from './FilePanel.classes';
import { IFilePanel } from './FilePanel.types';
import translations from './translations';

// #region MOCK DATA
const mockOnRemove = vi.fn();
const mockOnClose = vi.fn();
const mockOnRetry = vi.fn();

const rejectedFiles: IFilePanel['rejectedFiles'] = [
  {
    file: new File(['test'], 'RejectedFile-1.dxf', {
      type: 'text/plain',
    }),
    errors: [
      {
        code: 'file-invalid-type',
        message: 'File type must be image/jpeg,.jpg,.jpeg,image/png,.png,image/gif,.gif',
      },
    ],
  },
];

const acceptedFiles: IFilePanel['acceptedFiles'] = [
  new File(['test'], 'AcceptedFile-1.dxf', {
    type: 'text/plain',
  }),
  new File(['test'], 'AcceptedFile-2.txt', {
    type: 'text/plain',
  }),
  new File(['test'], 'AcceptedFile-1.pdf', {
    type: 'text/plain',
  }),
];

const acceptedFilesForNesting: IFilePanel['acceptedFiles'] = [
  new File(['test'], 'AA.pdf', {
    type: 'text/plain',
  }),
  new File(['test'], 'AA.dxf', {
    type: 'text/plain',
  }),
  new File(['test'], 'BB.dxf', {
    type: 'text/plain',
  }),
  new File(['test'], 'BB.ai', {
    type: 'text/plain',
  }),
  new File(['test'], 'CC.md', {
    type: 'text/plain',
  }),
  new File(['test'], 'CC.dxf', {
    type: 'text/plain',
  }),
  new File(['test'], 'CC.pdf', {
    type: 'text/plain',
  }),
  new File(['test'], 'CC.ai', {
    type: 'text/plain',
  }),
];
const sortedFileNamesForNesting = acceptedFilesForNesting.map((file) => file.name).sort();

const uploadFailedFile: FileWithStatus = {
  file: new File(['test'], 'UploadFailedFile.dxf', {
    type: 'text/plain',
  }),
  status: UploadStatus.Failed,
};

const uploadingFiles: IFilePanel['uploadingFiles'] = [
  {
    file: new File(['test'], 'UploadingFile-1.dxf', {
      type: 'text/plain',
    }),
    status: UploadStatus.Uploading,
    progress: 10,
  },
  {
    file: new File(['test'], 'UploadingFile-2.dxf', {
      type: 'text/plain',
    }),
    status: UploadStatus.Succeeded,
  },
  uploadFailedFile,
];
// #endregion

const FilePanelTemplate = (props: Partial<IFilePanel>) => {
  return (
    <FilePanel
      label="TestFilePanel"
      acceptedFiles={[]}
      rejectedFiles={[]}
      uploadingFiles={[]}
      onClose={mockOnClose}
      onRemove={mockOnRemove}
      onRetry={mockOnRetry}
      {...props}
    />
  );
};

describe('FilePanel', () => {
  it('should render FilePanel with label', () => {
    render(<FilePanelTemplate />);
    expect(screen.getByTestId(filePanelClasses.root)).toBeInTheDocument();
    expect(screen.getByTestId(filePanelClasses.label).textContent).toBe('TestFilePanel');
  });

  it('should render maximum 5 rejected-file alerts', () => {
    // Render 1 rejected file
    const { rerender } = render(<FilePanelTemplate rejectedFiles={rejectedFiles} />);
    let alerts = screen.getAllByRole('alert');
    expect(alerts.length).toBe(1);
    expect(alerts[0]).toHaveTextContent('RejectedFile-1.dxf');

    // Render with 6 rejected files (should only show 5)
    const multipleRejectedFiles = Array.from({ length: 6 }, (_, index) => ({
      file: new File(['test'], `RejectedFile-${index + 1}`, {
        type: 'text/plain',
      }),
      errors: [
        {
          code: 'file-invalid-type',
          message: 'File type must be image/jpeg,.jpg,.jpeg,image/png,.png,image/gif,.gif',
        },
      ],
    }));
    rerender(<FilePanelTemplate rejectedFiles={multipleRejectedFiles} />);
    alerts = screen.getAllByRole('alert');
    expect(alerts.length).toBe(5);
    expect(screen.getByText(translations['en-US'].fileErrorMsg)).toBeInTheDocument();
  });

  it('should render uploaded files with correct statuses', () => {
    render(<FilePanelTemplate acceptedFiles={acceptedFiles} uploadingFiles={uploadingFiles} />);
    expect(screen.getAllByTestId(fileItemClasses.root).length).toBe(6); // 3 accepted + 3 uploading
    expect(screen.getAllByTestId(fileStatusClasses['actions-succeeded']).length).toBe(4); // 3 accepted + 1 uploading with succeeded status
    expect(screen.getAllByTestId(fileStatusClasses['actions-failed']).length).toBe(1);
    expect(screen.getAllByTestId(fileStatusClasses['actions-uploading']).length).toBe(1);
  });

  it('should render custom file icons', () => {
    const { rerender } = render(<FilePanelTemplate acceptedFiles={acceptedFiles} />);
    expect(screen.getAllByTestId('MIxNovaFilePanel-fileIcon').length).toBe(2);
    expect(screen.getAllByTestId('MIxNovaFilePanel-subFileIcon').length).toBe(1);

    rerender(
      <FilePanelTemplate
        acceptedFiles={acceptedFiles}
        iconMapping={{
          dxf: <Icon data-testid="custom-dxf-icon" family="material" name="dxf_icon" />,
          txt: <Icon data-testid="custom-pdf-icon" family="material" name="txt_icon" />,
        }}
      />,
    );
    expect(screen.getAllByTestId('custom-dxf-icon').length).toBe(1);
    expect(screen.getAllByTestId('custom-pdf-icon').length).toBe(1);
    expect(screen.getAllByTestId('MIxNovaFilePanel-subFileIcon').length).toBe(1);
  });

  it('should render custom error message for rejected files', () => {
    render(
      <FilePanelTemplate
        rejectedFiles={rejectedFiles}
        getErrorMessage={(rejection) => `${rejection.errors[0].code}`}
      />,
    );
    expect(screen.getByText('file-invalid-type')).toBeInTheDocument();
  });

  it('should hide file sizes when hideSizes is true', () => {
    const { rerender } = render(<FilePanelTemplate acceptedFiles={acceptedFiles} uploadingFiles={uploadingFiles} />);
    expect(screen.getAllByTestId(fileItemClasses.fileSize).length).toBe(4); // 3 accepted + 1 uploading with succeeded status

    rerender(<FilePanelTemplate hideSizes />);
    expect(screen.queryByTestId(fileItemClasses.fileSize)).not.toBeInTheDocument();
  });

  it('should nest files under matching parentFileExtension if available, otherwise under first file alphabetically when parentFileExtension is a string', () => {
    render(<FilePanelTemplate parentFileExtension="pdf" acceptedFiles={acceptedFilesForNesting} />);

    expect(screen.getAllByTestId('MIxNovaFilePanel-fileIcon').length).toBe(3);
    expect(screen.getAllByTestId('MIxNovaFilePanel-subFileIcon').length).toBe(5);

    const fileNames = screen.getAllByTestId(fileItemClasses.fileName);
    expect(fileNames[0].textContent).toBe('AA.pdf');
    expect(fileNames[1].textContent).toBe('AA.dxf');

    // No file matching parentFileExtension -> nest under first file alphabetically
    expect(fileNames[2].textContent).toBe('BB.ai');
    expect(fileNames[3].textContent).toBe('BB.dxf');

    expect(fileNames[4].textContent).toBe('CC.pdf');
    expect(fileNames[5].textContent).toBe('CC.ai');
    expect(fileNames[6].textContent).toBe('CC.dxf');
    expect(fileNames[7].textContent).toBe('CC.md');
  });

  it('should nest files under first file alphabetically if parentFileExtension is undefined', () => {
    render(<FilePanelTemplate acceptedFiles={acceptedFilesForNesting} />);

    expect(screen.getAllByTestId('MIxNovaFilePanel-fileIcon').length).toBe(3);
    expect(screen.getAllByTestId('MIxNovaFilePanel-subFileIcon').length).toBe(5);

    const fileNames = screen.getAllByTestId(fileItemClasses.fileName);
    for (let i = 0; i < 8; i++) {
      expect(fileNames[i].textContent).toBe(sortedFileNamesForNesting[i]);
    }
  });

  it('should not nest files if parentFileExtension is null', () => {
    render(<FilePanelTemplate parentFileExtension={null} acceptedFiles={acceptedFilesForNesting} />);

    expect(screen.getAllByTestId('MIxNovaFilePanel-fileIcon').length).toBe(8);

    const fileNames = screen.getAllByTestId(fileItemClasses.fileName);
    for (let i = 0; i < 8; i++) {
      expect(fileNames[i].textContent).toBe(sortedFileNamesForNesting[i]);
    }
  });

  it('should call onClose alert when close icon is clicked', async () => {
    const user = userEvent.setup();

    render(<FilePanelTemplate rejectedFiles={rejectedFiles} />);
    await user.click(within(screen.getByRole('alert')).getByRole('button'));
    expect(mockOnClose).toHaveBeenCalledWith(rejectedFiles[0]);
  });

  it('should call onRetry when retry button is clicked', async () => {
    const user = userEvent.setup();

    render(<FilePanelTemplate uploadingFiles={[uploadFailedFile]} />);
    await user.click(screen.getByTestId(fileStatusClasses.retry));
    expect(mockOnRetry).toHaveBeenCalledWith(uploadFailedFile.file);
  });

  it('should render FileDeletePanel and call onRemove only after confirming delete', async () => {
    const user = userEvent.setup();

    // Cancel delete action
    render(<FilePanelTemplate uploadingFiles={[uploadFailedFile]} />);
    expect(screen.queryByTestId(filePanelClasses['deletePanel-root'])).not.toBeInTheDocument();
    expect(screen.queryByTestId(filePanelClasses['deletePanel-title'])).not.toBeInTheDocument();
    await user.click(
      screen.getByRole('button', {
        name: 'Cancel Action',
      }),
    );
    expect(screen.getByTestId(filePanelClasses['deletePanel-root'])).toBeInTheDocument();
    expect(screen.getByTestId(filePanelClasses['deletePanel-title'])).toBeInTheDocument();
    await user.click(screen.getByTestId(filePanelClasses['deletePanel-cancel']));
    expect(mockOnRemove).not.toHaveBeenCalled();

    // Confirm delete action
    await user.click(
      screen.getByRole('button', {
        name: 'Cancel Action',
      }),
    );
    await user.click(screen.getByTestId(filePanelClasses['deletePanel-delete']));
    expect(mockOnRemove).toHaveBeenCalledWith(uploadFailedFile.file);
  });
});
