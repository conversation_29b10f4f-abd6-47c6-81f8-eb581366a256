'use client';

import { Icon } from '@hxnova/icons';
import { Alert } from '@hxnova/react-components/Alert';
import { Badge } from '@hxnova/react-components/Badge';
import { Button } from '@hxnova/react-components/Button';
import { List } from '@hxnova/react-components/List';
import { Typography } from '@hxnova/react-components/Typography';
import clsx from 'clsx';
import groupBy from 'lodash/groupBy';
import sortBy from 'lodash/sortBy';
import { forwardRef, ReactElement, useMemo, useState } from 'react';
import { FileItem } from '../FileItem';
import { FileWithPath } from '../types';
import { useTranslate } from '../utils/locales';
import filePanelClasses from './FilePanel.classes';
import { IFilePanel } from './FilePanel.types';
import translations from './translations';

/**
 * Renders the file icon based on the file extension and whether the file is nested.
 * Uses the provided icon in iconMapping if available; otherwise, falls back to a default document icon.
 */
const renderFileIcon = (filename: string, isSubFile: boolean, mapping: NonNullable<IFilePanel['iconMapping']>) => {
  const fileNameParts = filename.split('.');
  const extension = fileNameParts[fileNameParts.length - 1];

  if (isSubFile) {
    const renderSubFileIcon = mapping[extension] ?? (
      <Icon data-testid="MIxNovaFilePanel-subFileIcon" family="material" name="description" />
    );
    return (
      <div
        sx={() => ({
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          minWidth: 56,
          fontSize: 24,
        })}
      >
        <div
          sx={(theme) => ({
            display: 'flex',
            color: theme.vars.palette.surfaceVariant,
          })}
        >
          <Icon family="material" name="subdirectory_arrow_right" />
        </div>
        <div
          sx={(theme) => ({
            display: 'flex',
            color: theme.vars.palette.onSurfaceVariant,
          })}
        >
          {renderSubFileIcon}
        </div>
      </div>
    );
  }

  const renderFileIcon = mapping[extension] ?? (
    <Icon data-testid="MIxNovaFilePanel-fileIcon" family="material" name="description" />
  );
  return (
    <div
      sx={{
        display: 'flex',
        fontSize: 24,
      }}
    >
      {renderFileIcon}
    </div>
  );
};

interface IFileDeletePanel {
  onDelete: () => void;
  onCancel: () => void;
  t: ReturnType<typeof useTranslate>;
}
const FileDeletePanel = ({ onDelete, onCancel, t }: IFileDeletePanel) => (
  <div
    className={filePanelClasses['deletePanel-root']}
    data-testid={filePanelClasses['deletePanel-root']}
    sx={(theme) => ({
      display: 'flex',
      alignItems: 'center',
      gap: theme.vars.sys.viewport.spacing.spaceBetween.horizontal.md,
    })}
  >
    <Typography
      className={filePanelClasses['deletePanel-title']}
      data-testid={`${filePanelClasses['deletePanel-title']}`}
      sx={{ fontWeight: 700 }}
      variant="labelSmall"
      noWrap
    >
      {t('removeFile')}
    </Typography>
    <div
      sx={(theme) => ({
        display: 'flex',
        gap: theme.vars.sys.viewport.spacing.spaceBetween.horizontal.xs,
        alignItems: 'center',
      })}
    >
      <Button
        className={filePanelClasses['deletePanel-delete']}
        data-testid={filePanelClasses['deletePanel-delete']}
        size="small"
        color="error"
        onClick={onDelete}
      >
        {t('Delete')}
      </Button>
      <Button
        className={filePanelClasses['deletePanel-cancel']}
        data-testid={filePanelClasses['deletePanel-cancel']}
        variant="text"
        size="small"
        onClick={onCancel}
      >
        {t('Cancel')}
      </Button>
    </div>
  </div>
);

export const FilePanel = forwardRef<HTMLDivElement, IFilePanel>((props, ref) => {
  const {
    label = 'Uploaded Files',
    hideSizes = false,
    rejectedFiles,
    acceptedFiles,
    uploadingFiles,
    parentFileExtension,
    getErrorMessage,
    onRemove,
    onClose,
    onRetry,
    iconMapping = {},
    className,
    ...restProps
  } = props;

  const t = useTranslate(translations);
  const [selectedFile, setSelectedFile] = useState<FileWithPath | null>(null);
  const uploadedFileCount = acceptedFiles.length + (uploadingFiles?.length ?? 0);

  const renderAcceptedFileItems = useMemo(() => {
    const sortedAcceptedFiles = sortBy(acceptedFiles, 'name');

    // group files by their base-name (without extension)
    // split by '.' and join back without the last part (extension)
    // e.g. "x.y.z.pdf" -> "x.y.z"
    const groupedAcceptedFiles = groupBy(sortedAcceptedFiles, (file) => {
      const nameParts = file.name.split('.');
      nameParts.pop();
      return nameParts.join('');
    });

    const parentFileExtRegex = new RegExp(`\\.${parentFileExtension}$`, 'i');
    let acceptedFileItems: ReactElement[] = [];

    Object.keys(groupedAcceptedFiles)
      .sort()
      .forEach((groupName) => {
        const filesInGroup = groupedAcceptedFiles[groupName];

        // Nest files when a parent file extension is specified.
        const shouldNest = parentFileExtension !== null;
        if (shouldNest) {
          /**
           * If a file with parent extension exists, files will be nested under it.
           * Otherwise, the group nests under the first file in alphabetical order.
           */
          filesInGroup.sort((a, b) =>
            parentFileExtRegex.test(a.name) ? -1 : parentFileExtRegex.test(b.name) ? 1 : a.name.localeCompare(b.name),
          );
        }

        acceptedFileItems = acceptedFileItems.concat(
          filesInGroup.map((file, index) => {
            const isSubFile = index > 0 && shouldNest;
            return (
              <FileItem
                key={`${file.name}-${index}`}
                icon={renderFileIcon(file.name, isSubFile, iconMapping)}
                name={file.name}
                size={file.size}
                hideSize={hideSizes}
                secondaryAction={
                  selectedFile?.name === file.name ? (
                    <FileDeletePanel
                      onDelete={() => {
                        setSelectedFile(null);
                        onRemove(file);
                      }}
                      onCancel={() => setSelectedFile(null)}
                      t={t}
                    />
                  ) : null
                }
                onCancel={() => {
                  setSelectedFile(file);
                }}
              />
            );
          }),
        );
      });

    return acceptedFileItems;
  }, [parentFileExtension, iconMapping, hideSizes, selectedFile, acceptedFiles, onRemove, t]);

  const renderUploadingFileItems = useMemo(
    () =>
      uploadingFiles?.map((uploadingFile, index) => (
        <FileItem
          key={`${uploadingFile.file.name}-${index}`}
          icon={renderFileIcon(uploadingFile.file.name, false, iconMapping)}
          name={uploadingFile.file.name}
          size={uploadingFile.file.size}
          hideSize={hideSizes}
          status={uploadingFile.status}
          progress={uploadingFile.progress}
          onRetry={() => {
            onRetry && onRetry(uploadingFile.file);
          }}
          secondaryAction={
            uploadingFile.file.name === selectedFile?.name ? (
              <FileDeletePanel
                onDelete={() => {
                  setSelectedFile(null);
                  onRemove(uploadingFile.file);
                }}
                onCancel={() => setSelectedFile(null)}
                t={t}
              />
            ) : null
          }
          onCancel={() => {
            setSelectedFile(uploadingFile.file);
          }}
        />
      )) || [],
    [uploadingFiles, iconMapping, hideSizes, selectedFile, onRemove, onRetry, t],
  );

  const renderRejectedAlerts = useMemo(
    () =>
      rejectedFiles.length > 0
        ? rejectedFiles.slice(0, 5).map((rejection, index) => (
            <Alert
              key={`${rejection.file.name}-${index}`}
              intensity="subtle"
              color="error"
              startDecorator={<Icon family="material" name="info" size={24} />}
              onClose={() => onClose(rejection)}
            >
              {getErrorMessage
                ? getErrorMessage(rejection)
                : t('uploadFailedMsg', {
                    '0': rejection.file.name,
                  })}
            </Alert>
          ))
        : undefined,
    [rejectedFiles, getErrorMessage, onClose, t],
  );

  return (
    <div
      ref={ref}
      className={clsx(filePanelClasses.root, className)}
      data-testid={filePanelClasses.root}
      sx={(theme) => ({
        display: 'flex',
        flexDirection: 'column',
        gap: theme.vars.sys.viewport.spacing.spaceBetween.vertical.md,
      })}
      {...restProps}
    >
      {rejectedFiles.length > 5 && (
        <Typography variant="labelMedium" sx={(theme) => ({ color: theme.vars.palette.system.onWarningContainer })}>
          {t('fileErrorMsg')}
        </Typography>
      )}

      {renderRejectedAlerts}

      <div
        sx={(theme) => ({
          display: 'flex',
          alignItems: 'center',
          gap: theme.vars.sys.viewport.spacing.spaceBetween.horizontal.xs,
        })}
      >
        <Typography variant="labelMedium" className={filePanelClasses.label} data-testid={filePanelClasses.label}>
          {label}
        </Typography>
        <Badge badgeContent={uploadedFileCount} />
      </div>

      {uploadedFileCount > 0 && (
        <List
          sx={(theme) => ({
            paddingBlock: theme.vars.sys.viewport.spacing.padding.topBottom.xs,
            borderRadius: theme.vars.sys.viewport.radius.xs,
            border: `1px solid ${theme.vars.palette.outlineVariant}`,
            backgroundColor: theme.vars.palette.surfaceContainer,
          })}
        >
          {[...renderAcceptedFileItems, ...renderUploadingFileItems]}
        </List>
      )}
    </div>
  );
});
