import generateUtilityClasses from '@mui/utils/generateUtilityClasses';

export interface FilePanelClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the label displayed above the list of uploaded files. */
  label: string;
  /** Class name applied to the root container of the delete panel. */
  'deletePanel-root': string;
  /** Class name applied to the title of the delete panel. */
  'deletePanel-title': string;
  /** Class name applied to the delete button in the delete panel. */
  'deletePanel-delete': string;
  /** Class name applied to the cancel button in the delete panel. */
  'deletePanel-cancel': string;
}

const filePanelClasses: FilePanelClasses = generateUtilityClasses('MIxNovaFilePanel', [
  'root',
  'label',
  'deletePanel-root',
  'deletePanel-title',
  'deletePanel-delete',
  'deletePanel-cancel',
]);

export default filePanelClasses;
