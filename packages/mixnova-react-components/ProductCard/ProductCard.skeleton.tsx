import { Skeleton } from '@hxnova/react-components/Skeleton';

export const ProductCardSkeleton = (
  <div sx={{ padding: 16, display: 'flex', flexDirection: 'column' }}>
    <div
      sx={{
        display: 'flex',
      }}
    >
      <Skeleton variant="rounded" width={52} height={52} />

      <div
        sx={{
          display: 'flex',
          flex: 1,
          marginLeft: 16,
          gap: 8,
        }}
      >
        <div
          sx={{
            display: 'flex',
            flex: 1,
            flexDirection: 'column',
            gap: 8,
          }}
        >
          <Skeleton variant="rounded" width="80%" height={24} />
          <Skeleton variant="rounded" width="30%" height={16} />
        </div>

        <Skeleton variant="rounded" width={40} height={40} />
      </div>
    </div>

    <Skeleton variant="rounded" width="30%" height={32} sx={{ marginTop: 16, marginBottom: 24 }} />

    <div
      sx={{
        display: 'flex',
        flexDirection: 'row',
      }}
    >
      <Skeleton variant="rounded" width="100%" height={24} sx={{ flex: 3, marginRight: 24 }} />
      <Skeleton variant="rounded" width="100%" height={24} sx={{ flex: 1 }} />
    </div>
    <div
      sx={{
        display: 'flex',
        flexDirection: 'row',
        marginTop: 16,
      }}
    >
      <Skeleton variant="rounded" width="100%" height={24} sx={{ flex: 3, marginRight: 24 }} />
      <Skeleton variant="rounded" width="100%" height={24} sx={{ flex: 1 }} />
    </div>
  </div>
);
