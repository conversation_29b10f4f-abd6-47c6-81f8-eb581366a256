import '@testing-library/jest-dom/vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, expect, it, vi } from 'vitest';
import { ProductCard } from './ProductCard';
import productCardClasses from './ProductCard.classes';
import { IProductCard } from './ProductCard.types';

function ProductCardTemplate(props: Partial<IProductCard>) {
  return <ProductCard headline="StringHeadline" icon={<div>TestIcon</div>} {...props} />;
}

describe('ProductCard', () => {
  it('should render ProductCard with required headline and icon', () => {
    const { rerender } = render(<ProductCardTemplate />);
    expect(screen.getByTestId(productCardClasses.root)).toBeInTheDocument();
    expect(screen.getByTestId(productCardClasses.stack)).toBeInTheDocument();
    expect(screen.getByTestId(productCardClasses.basicInfoStack)).toBeInTheDocument();
    expect(screen.getByTestId(productCardClasses.icon).textContent).toBe('TestIcon');
    expect(screen.getByTestId(productCardClasses.headlineSubheadingStack)).toBeInTheDocument();
    expect(screen.getByTestId(productCardClasses.headline).textContent).toBe('StringHeadline');

    rerender(<ProductCardTemplate headline={<div>CustomHeadline</div>} />);
    expect(screen.getByText('CustomHeadline')).toBeInTheDocument();
  });

  it('should render subheading and content', () => {
    const { rerender } = render(<ProductCardTemplate />);
    expect(screen.queryByTestId(productCardClasses.subheading)).not.toBeInTheDocument();
    expect(screen.queryByTestId(productCardClasses.content)).not.toBeInTheDocument();

    rerender(<ProductCardTemplate subheading="StringSubheading" content="StringContent" />);
    expect(screen.getByTestId(productCardClasses.subheading).textContent).toBe('StringSubheading');
    expect(screen.getByTestId(productCardClasses.content).textContent).toBe('StringContent');

    rerender(<ProductCardTemplate subheading={<div>CustomSubheading</div>} content={<div>CustomContent</div>} />);
    expect(screen.getByText('CustomSubheading')).toBeInTheDocument();
    expect(screen.getByText('CustomContent')).toBeInTheDocument();
  });

  it('should render menu actions', async () => {
    const user = userEvent.setup();
    const mockSettingsClick = vi.fn();

    const { rerender } = render(<ProductCardTemplate />);
    expect(screen.queryByTestId(productCardClasses.actionDropdownButton)).not.toBeInTheDocument();
    expect(screen.queryByTestId(productCardClasses.actionDropdownMenu)).not.toBeInTheDocument();
    expect(screen.queryByTestId(productCardClasses.menuItem)).not.toBeInTheDocument();

    // 1. menuActions is MenuItemProps[]
    rerender(
      <ProductCardTemplate
        menuActions={[
          {
            children: 'Delete',
          },
          {
            children: 'Settings',
            onClick: mockSettingsClick,
          },
        ]}
      />,
    );
    // Menu not open initially
    expect(screen.queryByTestId(productCardClasses.actionDropdownMenu)).not.toBeInTheDocument();
    expect(screen.queryByTestId(productCardClasses.menuItem)).not.toBeInTheDocument();
    // Open menu
    const menuButtonEl = screen.getByTestId(productCardClasses.actionDropdownButton);
    await user.click(menuButtonEl);
    expect(screen.getByTestId(productCardClasses.actionDropdownMenu)).toBeInTheDocument();
    expect(screen.getAllByTestId(productCardClasses.menuItem).length).toBe(2);
    // Click on a menu item
    await user.click(screen.getByText('Settings'));
    expect(mockSettingsClick).toHaveBeenCalled();
    expect(screen.queryByTestId(productCardClasses.actionDropdownMenu)).not.toBeInTheDocument();

    // 2. menuActions is ReactNode
    rerender(<ProductCardTemplate menuActions={<div>CustomMenuActions</div>} />);
    await user.click(menuButtonEl);
    expect(screen.getByText('CustomMenuActions')).toBeInTheDocument();
  });

  it('should render list of actions', async () => {
    const user = userEvent.setup();
    const mockCustomizedListItemClick = vi.fn();

    const { rerender } = render(<ProductCardTemplate />);
    expect(screen.queryByTestId(productCardClasses.list)).not.toBeInTheDocument();
    expect(screen.queryByTestId(productCardClasses.listItem)).not.toBeInTheDocument;
    expect(screen.queryByTestId(productCardClasses.listItemIcon)).not.toBeInTheDocument();
    expect(screen.queryByTestId(productCardClasses.listItemText)).not.toBeInTheDocument();

    rerender(
      <ProductCardTemplate
        listActions={[
          {
            children: 'ListItemProps',
          },
          <p>CustomListItem</p>,
          {
            text: 'CustomizedListItemProps',
            icon: <p>CustomizedListItemIcon</p>,
            onClick: mockCustomizedListItemClick,
          },
        ]}
      />,
    );
    expect(screen.getByTestId(productCardClasses.list)).toBeInTheDocument();
    expect(screen.getAllByTestId(productCardClasses.listItem).length).toBe(3);
    expect(screen.getByText('ListItemProps')).toBeInTheDocument();
    expect(screen.getByText('CustomListItem')).toBeInTheDocument();
    expect(screen.getByTestId(productCardClasses.listItemIcon).textContent).toBe('CustomizedListItemIcon');
    const listItemTextEl = screen.getByTestId(productCardClasses.listItemText);
    expect(listItemTextEl.textContent).toBe('CustomizedListItemProps');
    await user.click(listItemTextEl);
    expect(mockCustomizedListItemClick).toHaveBeenCalled();
  });
});
