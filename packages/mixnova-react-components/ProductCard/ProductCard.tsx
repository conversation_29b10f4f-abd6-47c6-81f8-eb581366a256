'use client';

import { Icon } from '@hxnova/icons';
import { CardRoot } from '@hxnova/react-components/Card';
import { IconButton } from '@hxnova/react-components/IconButton';
import { List } from '@hxnova/react-components/List';
import { ListDivider } from '@hxnova/react-components/ListDivider';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemButton } from '@hxnova/react-components/ListItemButton';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Menu } from '@hxnova/react-components/Menu';
import { MenuItem } from '@hxnova/react-components/MenuItem';
import { Typography } from '@hxnova/react-components/Typography';
import clsx from 'clsx';
import uniqueId from 'lodash/uniqueId';
import { forwardRef, Fragment, ReactNode, useState } from 'react';
import { CardLoadingManager } from '../Card/components/CardLoadingManager';
import { useCardLoading } from '../Card/hooks/useCardLoading';
import { isReactNode } from '../utils/functions/isReactNode';
import productCardClasses from './ProductCard.classes';
import { ProductCardSkeleton } from './ProductCard.skeleton';
import { CustomizedListItemProps, IProductCard } from './ProductCard.types';

export const ProductCard = forwardRef<HTMLDivElement, IProductCard>((props, ref) => {
  const {
    icon,
    headline,
    subheading,
    menuActions,
    listActions,
    content,
    loading = false,
    loadingMode = 'spinner',
    className,
    ...restProps
  } = props;

  const [componentUniqueId] = useState(() => uniqueId('MIxNovaProductCard-'));
  const [menuAnchorEl, setMenuAnchorEl] = useState<HTMLElement | null>(null);

  const { renderLoadingPanel, cardLoadingClass } = useCardLoading(loading, loadingMode);

  const renderHeadline =
    typeof headline === 'string' ? (
      <Typography
        className={productCardClasses.headline}
        data-testid={productCardClasses.headline}
        variant="titleSmall"
        sx={{
          fontWeight: 400,
        }}
      >
        {headline}
      </Typography>
    ) : (
      headline
    );

  const renderSubheading =
    typeof subheading === 'string' ? (
      <Typography
        className={productCardClasses.subheading}
        data-testid={productCardClasses.subheading}
        variant="labelSmall"
        sx={(theme) => ({
          color: theme.vars.palette.onSurfaceVariant,
        })}
      >
        {subheading}
      </Typography>
    ) : (
      subheading
    );

  const renderMenuActions = isReactNode(menuActions)
    ? menuActions
    : menuActions.map((item, index) => (
        <MenuItem
          key={index}
          className={productCardClasses.menuItem}
          data-testid={productCardClasses.menuItem}
          {...item}
        />
      ));

  const renderMenu = renderMenuActions && (
    <>
      <IconButton
        id={componentUniqueId}
        className={productCardClasses.actionDropdownButton}
        data-testid={productCardClasses.actionDropdownButton}
        onClick={(event) => setMenuAnchorEl(event.currentTarget)}
        aria-label="more"
        variant="neutral"
      >
        <Icon family="material" name="more_vert" />
      </IconButton>
      <Menu
        className={productCardClasses.actionDropdownMenu}
        data-testid={productCardClasses.actionDropdownMenu}
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={() => setMenuAnchorEl(null)}
        placement="bottom-end"
        slotProps={{
          listbox: {
            'aria-labelledby': componentUniqueId,
          },
        }}
      >
        {renderMenuActions}
      </Menu>
    </>
  );

  const renderBasicInfo = (
    <div
      className={productCardClasses.basicInfoStack}
      data-testid={productCardClasses.basicInfoStack}
      sx={(theme) => ({
        display: 'flex',
        gap: theme.vars.sys.viewport.spacing.spaceBetween.horizontal.md,
        alignItems: 'center',
      })}
    >
      <div
        className={productCardClasses.icon}
        data-testid={productCardClasses.icon}
        sx={{
          width: 40,
          height: 40,
          flexShrink: 0,
        }}
      >
        {icon}
      </div>

      <div
        data-testid={productCardClasses.headlineSubheadingStack}
        className={productCardClasses.headlineSubheadingStack}
        sx={(theme) => ({
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          flex: 1,
          gap: theme.vars.sys.viewport.spacing.spaceBetween.vertical['2xs'],
        })}
      >
        {renderHeadline}
        {renderSubheading}
      </div>
      {renderMenu}
    </div>
  );

  const renderContent =
    typeof content === 'string' ? (
      <Typography
        className={productCardClasses.content}
        data-testid={productCardClasses.content}
        variant="bodyMedium"
        sx={(theme) => ({
          color: theme.vars.palette.onSurface,
        })}
      >
        {content}
      </Typography>
    ) : (
      content
    );

  const renderList = listActions && listActions.length > 0 && (
    <List className={productCardClasses.list} data-testid={productCardClasses.list}>
      {listActions.map((item, index) => {
        let itemContent: ReactNode = null;

        // item is ReactNode
        if (isReactNode(item)) {
          itemContent = (
            <ListItem className={productCardClasses.listItem} data-testid={productCardClasses.listItem}>
              <ListItemButton>{item}</ListItemButton>
            </ListItem>
          );
        }

        // item is ListItemProps
        else if ('children' in item) {
          itemContent = (
            <ListItem className={productCardClasses.listItem} data-testid={productCardClasses.listItem} {...item} />
          );
        }

        // item is CustomizedListItemProps
        else {
          const { text, icon, onClick, ...otherItem } = item as CustomizedListItemProps;

          itemContent = (
            <ListItem
              className={productCardClasses.listItem}
              data-testid={productCardClasses.listItem}
              sx={(theme) => ({
                '--nova-listItem-color': theme.vars.palette.primary,
                '--nova-listItem-secondaryColor': theme.vars.palette.primary,
              })}
              {...otherItem}
            >
              <ListItemButton onClick={onClick}>
                <ListItemContent
                  data-testid={productCardClasses.listItemText}
                  className={productCardClasses.listItemText}
                  primary={text}
                />
                <ListItemDecorator
                  className={productCardClasses.listItemIcon}
                  data-testid={productCardClasses.listItemIcon}
                  sx={{ fontSize: 20 }}
                >
                  {icon}
                </ListItemDecorator>
              </ListItemButton>
            </ListItem>
          );
        }

        return (
          <Fragment key={index}>
            <ListDivider />
            {itemContent}
          </Fragment>
        );
      })}
    </List>
  );

  return (
    <CardRoot
      ref={ref}
      className={clsx(productCardClasses.root, cardLoadingClass, className)}
      data-testid={productCardClasses.root}
      {...restProps}
    >
      <CardLoadingManager loading={loading} loadingMode={loadingMode} skeleton={ProductCardSkeleton}>
        <div
          className={productCardClasses.stack}
          data-testid={productCardClasses.stack}
          sx={(theme) => ({
            display: 'flex',
            flexDirection: 'column',
            gap: theme.vars.sys.viewport.spacing.spaceBetween.vertical.md,
            padding: `${theme.vars.sys.viewport.spacing.padding.topBottom.md} ${theme.vars.sys.viewport.spacing.padding.leftRight.md}`,
          })}
        >
          {renderBasicInfo}
          {renderContent}
        </div>
        {renderList}
        {renderLoadingPanel}
      </CardLoadingManager>
    </CardRoot>
  );
});
