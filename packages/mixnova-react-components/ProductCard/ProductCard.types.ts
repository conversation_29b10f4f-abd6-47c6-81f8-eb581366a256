import { CardRootProps } from '@hxnova/react-components/Card';
import { ListItemProps } from '@hxnova/react-components/ListItem';
import { ListItemButtonProps } from '@hxnova/react-components/ListItemButton';
import { MenuItemProps } from '@hxnova/react-components/MenuItem';
import { ReactNode } from 'react';
import { ICardLoading } from '../Card/Card.types';

export type CustomizedListItemProps = Omit<ListItemProps, 'children' | 'onClick'> &
  Pick<ListItemButtonProps, 'onClick'> & {
    /**
     * The primary text to display on the left side of the list item.
     */
    text: ReactNode;
    /**
     * The icon element to display on the right side of the list item.
     */
    icon?: ReactNode;
  };

export type ProductCardListItem = NonNullable<IProductCard['listActions']>[number];

/**
 * @inheritPropsOf Nova [Card.Root](https://zeroheight.com/9a7698df1/p/38e9e0-cards/b/65548f)
 */
export type IProductCard = Omit<CardRootProps, 'children' | 'content'> &
  ICardLoading & {
    /**
     * The icon element to display.
     * It always shows the Logo of the product.
     */
    icon: ReactNode;
    /**
     * The main label of the card. It could be a string or a custom component.
     */
    headline: ReactNode;
    /**
     * A line of text that displays below the card headline. It could be a string or a custom component.
     */
    subheading?: ReactNode;
    /**
     * The content rendered between the header section and the actions list. It could be a string or a custom component.
     */
    content?: ReactNode;
    /**
     * The menu actions to display in the top-right `Menu`. This can be:
     * - An array of [MenuItemProps](https://zeroheight.com/9a7698df1/p/531cd9-menu/b/294882) objects used to render `MenuItem` elements in the built-in `Menu` component.
     * - A custom component
     */
    menuActions?: ReactNode | ReadonlyArray<MenuItemProps>;
    /**
     * The list of actions to display in the `List` component.
     *
     * Each element in the array can be:
     * - A simplified `CustomizedListItemProps` object with required `text` and optional `icon` properties, used to render a customized `ListItem`.
     * - A [ListItemProps]((https://zeroheight.com/9a7698df1/p/231da9-lists/b/723930)) object used to render a standard `ListItem`
     * - A custom component
     *
     * ```
     * type CustomizedListItemProps = {
     *   text: ReactNode;
     *   icon?: ReactNode;
     *   onClick: () => void;
     * }
     * ```
     */
    listActions?: ReadonlyArray<ReactNode | ListItemProps | CustomizedListItemProps>;
  };
