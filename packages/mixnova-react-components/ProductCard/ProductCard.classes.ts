import generateUtilityClasses from '@mui/utils/generateUtilityClasses';

export interface ProductCardClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the stack element that wraps basic info and content */
  stack: string;
  /** Class name applied to the icon element. */
  icon: string;
  /** Class name applied to the headline element. */
  headline: string;
  /** Class name applied to the subheading element. */
  subheading: string;
  /** Class name applied to the menu item element. */
  menuItem: string;
  /** Class name applied to the action dropdown button element. */
  actionDropdownButton: string;
  /** Class name applied to the action dropdown menu element. */
  actionDropdownMenu: string;
  /** Class name applied to the basic info stack element that wraps icon, headlineSubheading stack and menu */
  basicInfoStack: string;
  /** Class name applied to the headline and subheading stack element. */
  headlineSubheadingStack: string;
  /** Class name applied to the content element. */
  content: string;
  /** Class name applied to the list element. */
  list: string;
  /** Class name applied to the list item element. */
  listItem: string;
  /** Class name applied to the icon element in the customized ListItem. */
  listItemIcon: string;
  /** Class name applied to the text element in the customized ListItem. */
  listItemText: string;
}

const productCardClasses: ProductCardClasses = generateUtilityClasses('MIxNovaProductCard', [
  'root',
  'stack',
  'icon',
  'headline',
  'subheading',
  'menuItem',
  'actionDropdownButton',
  'actionDropdownMenu',
  'basicInfoStack',
  'headlineSubheadingStack',
  'content',
  'list',
  'listItem',
  'listItemIcon',
  'listItemText',
]);

export default productCardClasses;
