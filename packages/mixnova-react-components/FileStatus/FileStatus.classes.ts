import generateUtilityClasses from '@mui/utils/generateUtilityClasses';

export interface FileStatusClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the container of status indicators. */
  status: string;
  /** Class name applied to the `LinearProgress` element. */
  progress: string;
  /** Class name applied to the error message text when `status='failed'`. */
  failed: string;
  /** Class name applied to the actions element when `status='uploading'`. */
  'actions-uploading': string;
  /** Class name applied to the actions element when `status='failed'`. */
  'actions-failed': string;
  /** Class name applied to the actions element when `status='succeeded'`. */
  'actions-succeeded': string;
  /** Class name applied to the retry button shown when `status='failed'`. */
  retry: string;
}

const fileStatusClasses: FileStatusClasses = generateUtilityClasses('MIxNovaFileStatus', [
  'root',
  'progress',
  'status',
  'failed',
  'actions-uploading',
  'actions-failed',
  'actions-succeeded',
  'retry',
]);

export default fileStatusClasses;
