import '@testing-library/jest-dom/vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, expect, it, vi } from 'vitest';
import { UploadStatus } from '../types';
import { FileStatus } from './FileStatus';
import fileStatusClasses from './FileStatus.classes';

const mockOnRetry = vi.fn();
const mockOnCancel = vi.fn();

describe('FileStatus', () => {
  it('should render root and status elements', () => {
    render(<FileStatus />);
    expect(screen.getByTestId(fileStatusClasses.root)).toBeInTheDocument();
    expect(screen.getByTestId(fileStatusClasses.status)).toBeInTheDocument();
  });

  it('should render uploading status with progress and actions elements', () => {
    render(<FileStatus status={UploadStatus.Uploading} progress={20} />);
    expect(screen.getByTestId(fileStatusClasses.progress)).toBeInTheDocument();
    expect(screen.getByRole('progressbar')).toHaveStyle('--nova-linearProgress-percent: 20');
    expect(screen.getByTestId(fileStatusClasses['actions-uploading'])).toBeInTheDocument();
    expect(screen.queryByTestId(fileStatusClasses.failed)).not.toBeInTheDocument();
    expect(screen.queryByTestId(fileStatusClasses.retry)).not.toBeInTheDocument();
  });

  it('should render failed status with error message, actions and retry elements', async () => {
    render(<FileStatus status={UploadStatus.Failed} />);
    expect(screen.getByTestId(fileStatusClasses.failed).textContent).toBe('Upload failed');
    expect(screen.getByTestId(fileStatusClasses['actions-failed'])).toBeInTheDocument();
    expect(screen.getByTestId(fileStatusClasses.retry)).toBeInTheDocument();
    expect(screen.queryByTestId(fileStatusClasses.progress)).not.toBeInTheDocument();
  });

  it('should render succeeded status with actions element', () => {
    render(<FileStatus status={UploadStatus.Succeeded} />);
    expect(screen.getByTestId(fileStatusClasses['actions-succeeded'])).toBeInTheDocument();
    expect(screen.queryByTestId(fileStatusClasses.progress)).not.toBeInTheDocument();
    expect(screen.queryByTestId(fileStatusClasses.failed)).not.toBeInTheDocument();
    expect(screen.queryByTestId(fileStatusClasses.retry)).not.toBeInTheDocument();
  });

  it('should render custom error message', () => {
    render(<FileStatus status={UploadStatus.Failed} errorMessage="CustomErrorMessage" />);
    expect(screen.getByTestId(fileStatusClasses.failed).textContent).toBe('CustomErrorMessage');
  });

  it('should retry when retry button is clicked', async () => {
    const user = userEvent.setup();

    render(<FileStatus status={UploadStatus.Failed} onRetry={mockOnRetry} />);
    await user.click(screen.getByTestId(fileStatusClasses.retry));
    expect(mockOnRetry).toHaveBeenCalled();
  });

  it('should call onCancel only when no custom actions are provided', async () => {
    const user = userEvent.setup();

    const { rerender } = render(<FileStatus onCancel={mockOnCancel} />);
    await user.click(
      screen.getByRole('button', {
        name: 'Cancel Action',
      }),
    );
    expect(mockOnCancel).toHaveBeenCalled();

    mockOnCancel.mockClear();

    const onCustomActionsClick = vi.fn();
    rerender(
      <FileStatus
        actions={
          <button data-testid="custom-actions" onClick={onCustomActionsClick}>
            CustomActions
          </button>
        }
        onCancel={mockOnCancel}
      />,
    );
    expect(screen.queryByTestId('MIxNovaFileStatus-action-cancel')).not.toBeInTheDocument();
    await user.click(screen.getByTestId('custom-actions'));
    expect(mockOnCancel).not.toHaveBeenCalled();
    expect(onCustomActionsClick).toHaveBeenCalled();
  });
});
