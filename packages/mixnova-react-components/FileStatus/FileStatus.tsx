'use client';

import { Icon } from '@hxnova/icons';
import { Button } from '@hxnova/react-components/Button';
import { IconButton } from '@hxnova/react-components/IconButton';
import { LinearProgress } from '@hxnova/react-components/LinearProgress';
import { Typography } from '@hxnova/react-components/Typography';
import clsx from 'clsx';
import { forwardRef } from 'react';
import { UploadStatus } from '../types';
import { useTranslate } from '../utils/locales';
import fileStatusClasses from './FileStatus.classes';
import { IFileStatus } from './FileStatus.types';
import translations from './translations';

export const FileStatus = forwardRef<HTMLDivElement, IFileStatus>((props, ref) => {
  const t = useTranslate(translations);

  const {
    status = UploadStatus.Succeeded,
    progress = 0,
    actions,
    errorMessage = t('uploadFailed'),
    onCancel,
    onRetry,
    className,
    ...restProps
  } = props;

  const isUploading = status === UploadStatus.Uploading;
  const isFailed = status === UploadStatus.Failed;

  return (
    <div
      className={clsx(fileStatusClasses.root, className)}
      data-testid={fileStatusClasses.root}
      ref={ref}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-end',
      }}
      {...restProps}
    >
      <div
        className={fileStatusClasses.status}
        data-testid={fileStatusClasses.status}
        sx={(theme) => ({
          display: 'flex',
          alignItems: 'center',
          gap: theme.vars.sys.viewport.spacing.spaceBetween.horizontal.xs,
        })}
      >
        {isUploading && (
          <LinearProgress
            className={fileStatusClasses.progress}
            data-testid={fileStatusClasses.progress}
            variant="determinate"
            value={progress}
            aria-label="File Progress"
            sx={{
              minWidth: 54,
              flexGrow: 1,
            }}
          />
        )}

        {isFailed && (
          <Typography
            className={fileStatusClasses.failed}
            data-testid={fileStatusClasses.failed}
            variant="labelSmall"
            noWrap
            sx={(theme) => ({
              color: theme.vars.palette.onErrorContainer,
            })}
          >
            {errorMessage}
          </Typography>
        )}

        <div className={fileStatusClasses[`actions-${status}`]} data-testid={fileStatusClasses[`actions-${status}`]}>
          {actions || (
            <IconButton
              variant="neutral"
              onClick={onCancel}
              aria-label="Cancel Action"
              sx={(theme) => ({
                color: theme.vars.palette.onSurfaceVariant,
              })}
            >
              <Icon family="material" name="delete" />
            </IconButton>
          )}
        </div>
      </div>

      {isFailed && (
        <Button
          className={fileStatusClasses.retry}
          data-testid={fileStatusClasses.retry}
          sx={(theme) => ({
            marginTop: theme.vars.sys.viewport.spacing.spaceBetween.vertical.xs,
          })}
          size="small"
          variant="text"
          onClick={onRetry}
          endIcon={<Icon family="material" name="replay" />}
        >
          {t('retry')}
        </Button>
      )}
    </div>
  );
});
