import { H<PERSON><PERSON>ttributes, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ReactNode } from 'react';
import { UploadStatus } from '../types';

export type IFileStatus = Omit<HTMLAttributes<HTMLDivElement>, 'children'> & {
  /**
   * The file upload status.
   *
   * ```
   * enum UploadStatus {
   *  Uploading = 'uploading',
   *  Failed = 'failed',
   *  Succeeded = 'succeeded',
   * }
   * ```
   *
   * @default 'succeeded'
   */
  status?: UploadStatus;
  /**
   * The upload progress percentage, from 0 to 100, when `status="uploading"`.
   * @default 0
   */
  progress?: number;
  /**
   * Error message to display when `status="failed"`.
   * @default "Upload failed"
   */
  errorMessage?: string;
  /**
   * Custom actions element to replace the default cancel icon button. If provided, the `onCancel` callback is ignored.
   */
  actions?: ReactNode;
  /**
   * Callback fired when the retry button is clicked when `status="failed"`
   */
  onRetry?: Mouse<PERSON>ventHandler<HTMLButtonElement>;
  /**
   * Callback fired when the cancel icon button is clicked. This will be ignored if `actions` is provided.
   */
  onCancel?: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><HTMLButtonElement>;
};
