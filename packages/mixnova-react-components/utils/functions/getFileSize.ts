/**
 * Converts a file size in bytes into a human-readable string.
 *
 * - If the size is less than 1 MB, it returns the size in KB (e.g., "512.3KB").
 * - If the size is 1 MB or more, it returns the size in MB (e.g., "1.5MB").
 *
 * @param {size} size - file size in bytes
 * @return {string} - human readable file size
 **/
export const getFileSize = (size: number) =>
  size < 1024 * 1024 ? `${(size / 1024).toFixed(1)}KB` : `${(size / 1024 / 1024).toFixed(1)}MB`;
