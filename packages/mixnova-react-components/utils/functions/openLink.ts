/**
 * A utility function that determines how to open a link based on the current environment (CEF Browser, Web Browser).
 * 1) If in a CEF browser, the system default browser will be launched to open the link. The required CEF browser "Hexagon.Framework.WebBrowser" version should be higher than "2023.0.9000.9032".
 * 2) If a link is currently open in a web browser, how the link is opened depends on the target.
 * @param url The url of the link
 * @param target `'_blank'` or `'_self'` (defaults to `'_blank'`)
 */
export const openLink = (url: string | undefined, target = '_blank') => {
  if (!url) {
    return;
  }
  if (window.hxgnapi && window.hxgnapi.openLink) {
    // The openLink function has been add to the CEF browser recently, see https://hexagonmi.atlassian.net/browse/GEN-27918
    window.hxgnapi.openLink(url);
  } else {
    if (window.hxgnapi) {
      console.warn(
        `Please update your CEF browser 'Hexagon.Framework.WebBrowser' to a version after '2023.0.9000.9032'. The version you are currently using does not support opening links in the system default browser. `,
      );
    }
    window.open(url, target);
  }
};
