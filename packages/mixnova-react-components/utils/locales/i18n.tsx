import { type LocaleCode, useLanguage } from '@hxnova/react-components/Locale';
import get from 'lodash/get';
import { ReactNode, useMemo } from 'react';

export type FormatReactElementFn = (value: string) => ReactNode;
export type Translations = Partial<Record<LocaleCode, any>>;
type MissingKeysHandler = (path: string, key: string, language: LocaleCode) => void;

const defaultLanguage = 'en-US';

function getPath(obj: any, path: string) {
  if (!path) {
    return obj;
  }
  return path.split('.').reduce((acc, item) => (acc && acc[item] ? acc[item] : null), obj);
}

const getRichTextPosition = (s: string, key: string) => {
  const values: { from: number; to: number; tag: string }[] = [];
  let searchStart = 0;
  const tagLeft = `<${key}>`;
  const tagRight = `</${key}>`;
  let tagStart = s.indexOf(tagLeft, searchStart);
  let tagEnd = s.indexOf(tagRight, searchStart);
  while (tagStart > -1 && tagEnd > -1) {
    searchStart = tagStart + tagLeft.length;
    values.push({
      from: searchStart,
      to: tagEnd,
      tag: key,
    });
    searchStart = tagEnd + tagRight.length;
    tagStart = s.indexOf(tagLeft, searchStart);
    tagEnd = s.indexOf(tagRight, searchStart);
  }
  return values;
};

function format(translation: string, options: Record<string, any>) {
  let isNested = false;
  let hasFunctionOption = false;
  const results: React.ReactNode[] = [];
  let positions: { from: number; to: number; tag: string }[] = [];
  const optionKeys = Object.keys(options);

  optionKeys.forEach((key: string) => {
    const optionValue = options[key];
    if (typeof optionValue !== 'function') {
      if (!translation.includes(key)) {
        return translation;
      }
      translation = translation.replace(new RegExp(`\\{\\{${key}\\}\\}`, 'g'), optionValue?.toString());
    } else {
      hasFunctionOption = true;
      if (optionKeys.length > 1) {
        positions = positions.concat(getRichTextPosition(translation, key));
      }
    }
  });

  // Pure string replace, directly return.
  if (!hasFunctionOption) {
    return translation;
  }

  if (optionKeys.length === 1) {
    positions = getRichTextPosition(translation, optionKeys[0]);
  }
  positions.sort((a, b) => a.from - b.from);

  let start = 0;
  positions.forEach((position, index) => {
    if (start > position.from) {
      isNested = true;
    }
    results.push(translation.substring(start, position.from - (position.tag.length + 2)));
    results.push(options[position.tag](translation.substring(position.from, position.to), index));
    start = position.to + (position.tag.length + 3);
  });
  results.push(translation.substring(start));
  if (isNested && process.env.NODE_ENV !== 'production') {
    throw new Error(`[Localization] - "${translation}" includes nest tag`);
  }
  return results;
}

export function initTranslations(missingKeysHandler?: MissingKeysHandler) {
  return function useTranslate(translations: Translations, path = '') {
    // Use NovaProvider to store language
    const userLanguage = useLanguage();

    return useMemo(() => {
      const translationDictionary = translations[userLanguage];
      return function t(key: string, options?: Record<string, any>) {
        const subTranslationDictionary = getPath(translationDictionary, path) || {};
        let translation = subTranslationDictionary[key];

        if (!translation) {
          if (process.env.NODE_ENV !== 'production') {
            missingKeysHandler && missingKeysHandler(path, key, userLanguage);
            console.warn(`[Localization] - Missing translation for key -${key}- in ${userLanguage} language.`);
          }
          // if the translation in current language is not available, find it in the default language dictionary
          translation = path
            ? get(translations[defaultLanguage], `${path}.${key}`)
            : translations[defaultLanguage][key];
        }

        if (!options) {
          return translation;
        }

        return format(translation, options);
      };
    }, [userLanguage, path, translations]);
  };
}
