'use client';

import { CircularProgress } from '@hxnova/react-components/CircularProgress';
import { Typography } from '@hxnova/react-components/Typography';
import { styled } from '@pigment-css/react';
import clsx from 'clsx';
import { forwardRef, useMemo } from 'react';
import loadingPanelClasses from './LoadingPanel.classes';
import { ILoadingPanel } from './LoadingPanel.types';

const StyledBackdrop = styled('div', {
  shouldForwardProp: (prop) => prop !== 'open',
})<{ open: boolean }>({
  position: 'fixed',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  top: 0,
  right: 0,
  bottom: 0,
  left: 0,
  backgroundColor: 'rgba(0, 0, 0, 0.16)',
  zIndex: 1400,
  transitionProperty: 'visibility opacity',
  opacity: 1,
  transitionDuration: '0.225s',
  transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)',
  variants: [
    {
      props: { open: false },
      style: {
        opacity: 0,
        visibility: 'hidden',
        transitionDuration: '0.195s',
      },
    },
  ],
});

export const LoadingPanel = forwardRef<HTMLDivElement, ILoadingPanel>((props, ref) => {
  const { open = false, loader, loadingTitle, loadingMessage, className, ...restProps } = props;

  const renderTitle = useMemo(() => {
    if (!loadingTitle) return null;
    return typeof loadingTitle === 'string' ? (
      <Typography
        className={loadingPanelClasses.loadingTitle}
        data-testid={loadingPanelClasses.loadingTitle}
        variant="titleMedium"
        sx={(theme) => ({
          fontWeight: 400,
          marginTop: theme.vars.sys.viewport.spacing.spaceBetween.vertical.lg,
        })}
      >
        {loadingTitle}
      </Typography>
    ) : (
      loadingTitle
    );
  }, [loadingTitle]);

  const renderMessage = useMemo(() => {
    if (!loadingMessage) return null;
    return typeof loadingMessage === 'string' ? (
      <Typography
        className={loadingPanelClasses.loadingMessage}
        data-testid={loadingPanelClasses.loadingMessage}
        variant="titleSmall"
        sx={(theme) => ({
          fontWeight: 400,
          marginTop: theme.vars.sys.viewport.spacing.spaceBetween.vertical.xs,
          color: theme.vars.palette.onSurfaceVariant,
        })}
      >
        {loadingMessage}
      </Typography>
    ) : (
      loadingMessage
    );
  }, [loadingMessage]);

  const renderLoader = useMemo(
    () =>
      loader || (
        <CircularProgress
          variant="indeterminate"
          size={48}
          className={loadingPanelClasses.progress}
          data-testid={loadingPanelClasses.progress}
        />
      ),
    [loader],
  );

  return (
    <StyledBackdrop
      open={open}
      ref={ref}
      className={clsx(loadingPanelClasses.root, className)}
      data-testid={loadingPanelClasses.root}
      {...restProps}
    >
      <div
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        {renderLoader}
        {renderTitle}
        {renderMessage}
      </div>
    </StyledBackdrop>
  );
});
