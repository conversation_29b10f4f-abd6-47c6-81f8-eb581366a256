import { HTMLAttributes, ReactNode } from 'react';

export type ILoadingPanel = Omit<HTMLAttributes<HTMLDivElement>, 'children'> & {
  /**
   * Determine whether the loading panel is visible or not
   * @default false
   */
  open?: boolean;
  /**
   * The loader indicates the loading progress
   * @default <CircularProgress variant="indeterminate" />
   */
  loader?: ReactNode;
  /**
   * The title to display under the loader. It could be a string or a custom component.
   */
  loadingTitle?: ReactNode;
  /**
   *  The message to display under the title. It could be a string or a custom component.
   */
  loadingMessage?: ReactNode;
};
