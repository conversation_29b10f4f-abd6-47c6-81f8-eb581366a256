'use client';

import { Typography } from '@hxnova/react-components/Typography';
import clsx from 'clsx';
import { forwardRef } from 'react';
import emptyStateClasses from './EmptyState.classes';
import { IEmptyState } from './EmptyState.types';

export const EmptyState = forwardRef<HTMLDivElement, IEmptyState>((props, ref) => {
  const { size = 'medium', icon, header, description, actions, className, ...restProps } = props;

  const isSmall = size === 'small';

  const renderIcon = icon && (
    <div
      sx={(theme) => ({
        marginBottom: theme.vars.sys.viewport.spacing.spaceBetween.vertical.md,
        color: theme.vars.palette.onSurfaceVariant,
        fontSize: 40,
      })}
      className={emptyStateClasses.icon}
      data-testid={emptyStateClasses.icon}
    >
      {icon}
    </div>
  );

  const renderHeader =
    typeof header === 'string' ? (
      <Typography
        variant={isSmall ? 'titleMedium' : 'titleLarge'}
        sx={{
          fontWeight: 400,
        }}
        className={emptyStateClasses.header}
        data-testid={emptyStateClasses.header}
      >
        {header}
      </Typography>
    ) : (
      header
    );

  const renderDescription = description && (
    <div
      sx={(theme) => ({
        marginTop: theme.vars.sys.viewport.spacing.spaceBetween.vertical.xs,
      })}
    >
      {typeof description === 'string' ? (
        <Typography
          variant={isSmall ? 'bodySmall' : 'bodyMedium'}
          sx={(theme) => ({
            color: theme.vars.palette.onSurfaceVariant,
          })}
          className={emptyStateClasses.description}
          data-testid={emptyStateClasses.description}
        >
          {description}
        </Typography>
      ) : (
        description
      )}
    </div>
  );

  const renderActions = Array.isArray(actions) && (
    <div
      sx={(theme) => ({
        display: 'flex',
        marginTop: theme.vars.sys.viewport.spacing.spaceBetween.vertical.lg,
        gap: theme.vars.sys.viewport.spacing.spaceBetween.horizontal.md,
        flexDirection: 'column',
        [theme.breakpoints.up('sm')]: {
          flexDirection: 'row',
        },
      })}
    >
      {actions}
    </div>
  );

  return (
    <div
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        height: '100%',
        width: '100%',
        marginInline: 'auto',
      }}
      className={clsx(emptyStateClasses.root, className)}
      data-testid={emptyStateClasses.root}
      ref={ref}
      {...restProps}
    >
      {renderIcon}
      {renderHeader}
      {renderDescription}
      {renderActions}
    </div>
  );
});
