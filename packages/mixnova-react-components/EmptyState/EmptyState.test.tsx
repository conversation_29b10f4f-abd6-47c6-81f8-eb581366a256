import { Button, buttonClasses } from '@hxnova/react-components/Button';
import { typographyClasses } from '@hxnova/react-components/Typography';
import '@testing-library/jest-dom/vitest';
import { cleanup, render, screen } from '@testing-library/react';
import { afterEach, describe, expect, it } from 'vitest';
import { EmptyState } from './EmptyState';
import emptyStateClasses from './EmptyState.classes';

afterEach(() => {
  cleanup();
});

describe('EmptyState', () => {
  it('should correctly render root element', () => {
    render(<EmptyState header="TestHeader" />);
    expect(screen.getByTestId(emptyStateClasses.root)).toBeInTheDocument();
  });

  it('should correctly render icon element', () => {
    render(<EmptyState header="TestHeader" />);
    expect(screen.queryByTestId(emptyStateClasses.icon)).not.toBeInTheDocument();

    render(<EmptyState header="TestHeader" icon={<p>TestIcon</p>} />);
    expect(screen.getByTestId(emptyStateClasses.icon).textContent).toBe('TestIcon');
  });

  it('should correctly render header element', () => {
    const { rerender } = render(<EmptyState header={undefined} />);
    expect(screen.queryByTestId(emptyStateClasses.header)).not.toBeInTheDocument();

    rerender(<EmptyState header="TestHeader" />);
    expect(screen.getByTestId(emptyStateClasses.header).textContent).toBe('TestHeader');

    rerender(<EmptyState header={<p>TestHeader</p>} />);
    expect(screen.getByText('TestHeader')).toBeInTheDocument();
  });

  it('should correctly render description element', () => {
    const { rerender } = render(<EmptyState header="TestHeader" description="" />);
    expect(screen.queryByTestId(emptyStateClasses.description)).not.toBeInTheDocument();

    rerender(<EmptyState header="TestHeader" description="TestDescription" />);
    expect(screen.getByTestId(emptyStateClasses.description).textContent).toBe('TestDescription');

    rerender(<EmptyState header="TestHeader" description={<p>TestDescription</p>} />);
    expect(screen.getByText('TestDescription')).toBeInTheDocument();
  });

  it('should correctly render actions elements', () => {
    render(
      <EmptyState
        header="TestHeader"
        actions={[
          <Button key="1" variant="filled">
            Request access
          </Button>,
          <Button key="2" variant="outlined">
            Return to dashboard
          </Button>,
        ]}
      />,
    );
    expect(screen.getByText('Request access')).toHaveClass(buttonClasses.filled);
    expect(screen.getByText('Return to dashboard')).toHaveClass(buttonClasses.outlined);
  });

  it('should correctly render EmptyState with specified size', () => {
    const { rerender } = render(<EmptyState header="TestHeader" description="TestDescription" size="small" />);
    expect(screen.getByText('TestHeader')).toHaveClass(typographyClasses.titleMedium);
    expect(screen.getByText('TestDescription')).toHaveClass(typographyClasses.bodySmall);

    rerender(<EmptyState header="TestHeader" description="TestDescription" size="medium" />);
    expect(screen.getByText('TestHeader')).toHaveClass(typographyClasses.titleLarge);
    expect(screen.getByText('TestDescription')).toHaveClass(typographyClasses.bodyMedium);
  });
});
