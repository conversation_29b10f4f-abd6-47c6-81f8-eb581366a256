import generateUtilityClasses from '@mui/utils/generateUtilityClasses';

export interface EmptyStateClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the icon element. */
  icon: string;
  /** Class name applied to the header element. */
  header: string;
  /** Class name applied to the description element. */
  description: string;
}

const emptyStateClasses: EmptyStateClasses = generateUtilityClasses('MIxNovaEmptyState', [
  'root',
  'icon',
  'header',
  'description',
]);

export default emptyStateClasses;
