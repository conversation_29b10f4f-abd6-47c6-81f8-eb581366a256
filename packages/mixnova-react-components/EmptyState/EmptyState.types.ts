import { HTMLAttributes, ReactElement, ReactNode } from 'react';

export type IEmptyState = Omit<HTMLAttributes<HTMLDivElement>, 'children'> & {
  /**
   * The size of the component.
   *
   * - `small` is equivalent to the dense styling with smaller text size. It is used for smaller component, such as Card.
   *
   * - `medium` is used for larger screen, such as Table, Error page.
   * @default 'medium'
   */
  size?: 'small' | 'medium';

  /**
   * The Icon element displayed on the top of component. This could be an Icon or an image.
   */
  icon?: ReactNode;

  /**
   * The header of the component. It could be a string or a custom component.
   */
  header: ReactNode;

  /**
   * The description for additional information. It could be a string or a custom component.
   */
  description?: ReactNode;

  /**
   *  Action buttons for empty state component.
   */
  actions?: ReadonlyArray<ReactElement>;
};
