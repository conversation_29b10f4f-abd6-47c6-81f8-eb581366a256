{"name": "@hxnova/mi-react-components", "version": "1.0.0-alpha.0", "main": "dist/cjs/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "sideEffects": false, "license": "MIT", "scripts": {"test": "vitest", "test:ci": "vitest run --coverage", "coverage": "vitest --coverage", "build": "rm -rf dist && pnpm run build:esm && pnpm run build:cjs", "build:cjs": "tsc -p tsconfig.cjs.json", "build:esm": "tsc -p tsconfig.json"}, "dependencies": {"@hxnova/icons": "1.0.0-alpha.0", "@hxnova/react-components": "1.0.0-alpha.10", "@hxnova/themes": "1.0.0-alpha.11", "@mui/utils": "^7.1.0", "@pigment-css/react": "^0.0.30", "clsx": "^2.1.1", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8"}, "devDependencies": {"@babel/plugin-transform-export-namespace-from": "^7.27.1", "@pigment-css/vite-plugin": "^0.0.30", "@testing-library/jest-dom": "^6.6.2", "@testing-library/react": "^16.0.1", "@types/lodash": "^4.17.17", "@types/node": "^22.10.2", "@types/react": "^18.2.67", "@types/react-dom": "^18.2.67", "@vitejs/plugin-react": "^4.3.4", "jsdom": "^25.0.1", "typescript": "^5.4.2"}, "keywords": ["hexagon", "nova", "hmi", "manufacturing intelligence", "components", "react", "ui", "mixnova"]}