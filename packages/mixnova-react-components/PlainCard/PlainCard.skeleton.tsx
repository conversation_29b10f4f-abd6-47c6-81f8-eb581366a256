import { Skeleton } from '@hxnova/react-components/Skeleton';

export const PlainCardSkeleton = (
  <div
    sx={{
      display: 'flex',
      flexDirection: 'column',
    }}
  >
    <Skeleton variant="rounded" width={40} height={40} />
    <Skeleton variant="rounded" width="70%" height={24} sx={{ marginTop: 10 }} />
    <Skeleton
      variant="rounded"
      width="100%"
      height={32}
      sx={(theme) => ({ marginBlock: theme.vars.sys.viewport.spacing.spaceBetween.vertical.lg })}
    />
    <Skeleton variant="rounded" width="25%" height={24} />
  </div>
);
