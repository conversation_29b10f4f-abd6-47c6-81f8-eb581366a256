import { CardRootProps } from '@hxnova/react-components/Card';
import { ReactNode } from 'react';
import { ICardActions, ICardLoading } from '../Card/Card.types';

/**
 * @inheritPropsOf Nova [Card.Root](https://zeroheight.com/9a7698df1/p/38e9e0-cards/b/65548f)
 */
export type IPlainCard = Omit<CardRootProps, 'content' | 'children'> &
  ICardActions &
  ICardLoading & {
    /**
     * The color palette / theme to use for the card background / text / buttons.
     * @default 'neutral'.
     */
    color?: 'neutral' | 'success' | 'info' | 'primary';

    /**
     * The icon to display above the headline in the top-left corner of the card
     */
    icon?: ReactNode;

    /**
     * The main text to display below the icon in the top-left corner of the card. It could be a string or a custom component.
     */
    headline: ReactNode;

    /**
     * The main body content to display in the card. It could be a string or a custom component.
     */
    content?: ReactNode;

    /**
     * If `true`, the spacing in the card will be condensed, width will be reduced, and the text size of heading will be smaller
     * @default false
     */
    dense?: boolean;
  };
