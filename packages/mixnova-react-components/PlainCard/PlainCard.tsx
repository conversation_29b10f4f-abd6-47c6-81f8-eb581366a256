'use client';

import { CardContent } from '@hxnova/react-components/Card';
import { Typography } from '@hxnova/react-components/Typography';
import clsx from 'clsx';
import { forwardRef, useMemo } from 'react';
import { CardLoadingManager } from '../Card/components/CardLoadingManager';
import { useCardActions } from '../Card/hooks/useCardActions';
import { useCardLoading } from '../Card/hooks/useCardLoading';
import { isReactNode } from '../utils/functions/isReactNode';
import plainCardClasses from './PlainCard.classes';
import { PlainCardSkeleton } from './PlainCard.skeleton';
import { StyledButton, StyledCardRoot, StyledContentContainer, StyledIconContainer } from './PlainCard.styled';
import { IPlainCard } from './PlainCard.types';

export const PlainCard = forwardRef<HTMLDivElement, IPlainCard>((props, ref) => {
  const {
    color = 'neutral',
    dense = false,
    headline,
    content,
    icon,
    actions,
    actionButtons,
    loading = false,
    loadingMode = 'spinner',
    className,
    ...restProps
  } = props;

  const defaultButtons = actionButtons?.map((button, index) =>
    isReactNode(button) ? (
      button
    ) : (
      <StyledButton
        key={`${plainCardClasses.action}-${index}`}
        className={plainCardClasses.action}
        data-testid={plainCardClasses.action}
        cardColor={color}
        variant="filled"
        size={dense ? 'small' : 'medium'}
        {...button}
      />
    ),
  );

  const renderIcon = useMemo(() => {
    if (!icon) return null;
    return (
      <StyledIconContainer
        cardColor={color}
        dense={dense}
        className={plainCardClasses.icon}
        data-testid={plainCardClasses.icon}
      >
        {icon}
      </StyledIconContainer>
    );
  }, [icon, color, dense]);

  const renderHeadline = useMemo(() => {
    if (!headline) return null;
    return (
      <div
        className={plainCardClasses.headlineContainer}
        data-testid={plainCardClasses.headlineContainer}
        sx={(theme) => ({
          marginBottom: theme.vars.sys.viewport.spacing.spaceBetween.vertical.lg,
        })}
      >
        {typeof headline === 'string' ? (
          <Typography
            variant={dense ? 'titleMedium' : 'titleLarge'}
            className={plainCardClasses.headline}
            data-testid={plainCardClasses.headline}
            sx={{
              fontWeight: 400,
            }}
          >
            {headline}
          </Typography>
        ) : (
          headline
        )}
      </div>
    );
  }, [headline, dense]);

  const renderContent = useMemo(() => {
    if (!content) return null;
    return typeof content === 'string' ? (
      <StyledContentContainer
        className={plainCardClasses.contentContainer}
        data-testid={plainCardClasses.contentContainer}
        dense={dense}
      >
        <Typography
          variant={dense ? 'bodySmall' : 'bodyMedium'}
          className={plainCardClasses.content}
          data-testid={plainCardClasses.content}
        >
          {content}
        </Typography>
      </StyledContentContainer>
    ) : (
      content
    );
  }, [content, dense]);

  const { renderActions } = useCardActions({
    actions,
    actionButtons: defaultButtons,
    skip: !actions && !actionButtons,
    classes: plainCardClasses,
    padding: 0,
  });

  const { renderLoadingPanel, cardLoadingClass } = useCardLoading(loading, loadingMode);

  return (
    <StyledCardRoot
      dense={dense}
      cardColor={color}
      ref={ref}
      className={clsx(plainCardClasses.root, cardLoadingClass, className)}
      data-testid={plainCardClasses.root}
      {...restProps}
    >
      <CardLoadingManager loading={loading} loadingMode={loadingMode} skeleton={PlainCardSkeleton}>
        <CardContent
          sx={{
            padding: 0,
            gap: 0,
          }}
        >
          {renderIcon}
          {renderHeadline}
          {renderContent}
        </CardContent>
        {renderActions}
        {renderLoadingPanel}
      </CardLoadingManager>
    </StyledCardRoot>
  );
});
