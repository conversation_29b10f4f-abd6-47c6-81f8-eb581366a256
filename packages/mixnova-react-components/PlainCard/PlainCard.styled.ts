import { Button } from '@hxnova/react-components/Button';
import { CardRoot } from '@hxnova/react-components/Card';
import { styled } from '@pigment-css/react';
import { IPlainCard } from './PlainCard.types';

type StyledSlotProps = {
  cardColor?: IPlainCard['color'];
  dense?: IPlainCard['dense'];
};
const shouldForwardProp = (prop: string) => prop !== 'cardColor' && prop !== 'dense';

export const StyledIconContainer = styled('div', {
  shouldForwardProp,
})<StyledSlotProps>(({ theme }) => ({
  lineHeight: 0,
  marginBottom: 8,
  variants: [
    {
      props: { cardColor: 'neutral' },
      style: {
        color: theme.vars.palette.primary,
      },
    },
    {
      props: { cardColor: 'success' },
      style: {
        color: theme.vars.palette.system.onSuccessContainer,
      },
    },
    {
      props: { cardColor: 'info' },
      style: {
        color: theme.vars.palette.system.onInfoContainer,
      },
    },
    {
      props: { cardColor: 'primary' },
      style: {
        color: theme.vars.palette.onPrimaryContainer,
      },
    },
    {
      props: { dense: true },
      style: {
        fontSize: 24,
      },
    },
    {
      props: { dense: false },
      style: {
        fontSize: 32,
      },
    },
  ],
}));

export const StyledCardRoot = styled(CardRoot, {
  shouldForwardProp,
})<StyledSlotProps>(({ theme }) => ({
  maxWidth: '100%',
  variants: [
    {
      props: { dense: true },
      style: {
        padding: `${theme.vars.sys.viewport.spacing.padding.topBottom.md} ${theme.vars.sys.viewport.spacing.padding.leftRight.lg}`,
        width: 260,
      },
    },
    {
      props: { dense: false },
      style: {
        padding: `${theme.vars.sys.viewport.spacing.padding.topBottom.lg} ${theme.vars.sys.viewport.spacing.padding.leftRight.xl}`,
        width: 360,
      },
    },
    {
      props: { cardColor: 'neutral' },
      style: {
        backgroundColor: theme.vars.palette.surfaceContainer,
        color: theme.vars.palette.onSurface,
      },
    },
    {
      props: { cardColor: 'success' },
      style: {
        backgroundColor: theme.vars.palette.system.successContainer,
        color: theme.vars.palette.onSurface,
      },
    },
    {
      props: { cardColor: 'info' },
      style: {
        backgroundColor: theme.vars.palette.system.infoContainer,
        color: theme.vars.palette.onSurface,
      },
    },
    {
      props: { cardColor: 'primary' },
      style: {
        backgroundColor: theme.vars.palette.system.onInfoContainer,
        color: theme.vars.palette.onPrimaryContainer,
      },
    },
  ],
}));

export const StyledButton = styled(Button, {
  shouldForwardProp,
})<StyledSlotProps>(({ theme }) => ({
  variants: [
    {
      props: (props) => props.cardColor === 'neutral' && !props.disabled,
      style: {
        backgroundColor: theme.vars.palette.primary,
        color: theme.vars.palette.onPrimary,
      },
    },
    {
      props: (props) => props.cardColor === 'success' && !props.disabled,
      style: {
        backgroundColor: theme.vars.palette.system.onSuccessContainer,
        color: theme.vars.palette.onPrimary,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.onSuccessContainer}, ${theme.vars.palette.system.successContainer} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.system.onSuccessContainer}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.onSuccessContainer}, ${theme.vars.palette.system.successContainer} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.system.onSuccessContainer}, ${theme.vars.palette.system.successContainer} ${theme.vars.palette.stateLayers.pressOnSurface})`,
        },
      },
    },
    {
      props: (props) => props.cardColor === 'info' && !props.disabled,
      style: {
        backgroundColor: theme.vars.palette.system.onInfoContainer,
        color: theme.vars.palette.onPrimary,
      },
    },
    {
      props: (props) => props.cardColor === 'primary' && !props.disabled,
      style: {
        backgroundColor: theme.vars.palette.surfaceContainer,
        color: theme.vars.palette.onSurface,
        '&:hover': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.vars.palette.surfaceContainer}`,
          outlineOffset: 2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
        },
      },
    },
  ],
}));

export const StyledContentContainer = styled('div', {
  shouldForwardProp,
})<StyledSlotProps>(({ theme }) => ({
  variants: [
    {
      props: { dense: true },
      style: {
        marginBottom: theme.vars.sys.viewport.spacing.spaceBetween.vertical.lg,
      },
    },
    {
      props: { dense: false },
      style: {
        marginBottom: 48,
      },
    },
  ],
}));
