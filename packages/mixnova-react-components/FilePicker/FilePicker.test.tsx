import '@testing-library/jest-dom/vitest';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { beforeEach } from 'node:test';
import { describe, expect, it, vi } from 'vitest';
import { FilePicker } from './FilePicker';
import filePickerClasses from './FilePicker.classes';
import { IFilePicker } from './FilePicker.types';
import { dropOverlayClasses } from './components/DropOverlay';
import { filePickerBlockClasses } from './components/FilePickerBlock';

const mockOnDropped = vi.fn();

const FilePickerTemplate = (props: Partial<IFilePicker>) => (
  <FilePicker
    onDropped={mockOnDropped}
    dropZoneOptions={{
      accept: {
        'image/jpeg': ['.jpg', '.jpeg'],
        'image/png': ['.png'],
        'image/gif': ['.gif'],
      },
      noClick: true,
      noKeyboard: true,
      maxFiles: 100,
      maxSize: 1000,
      multiple: true,
    }}
    {...props}
  />
);

describe('FilePicker', () => {
  describe('Rendering', () => {
    it('should render FilePicker', () => {
      render(<FilePickerTemplate />);
      expect(screen.getByTestId(filePickerClasses.root)).toBeInTheDocument();
      expect(screen.getByTestId(filePickerClasses.input)).toBeInTheDocument();
    });

    it('should render default and custom DropOverlayComponent', async () => {
      const { rerender } = render(
        <FilePickerTemplate
          DropOverlayComponentProps={{
            header: 'TestDropOverlayHeader',
          }}
        />,
      );
      let inputEl = screen.getByTestId(filePickerClasses.input);
      expect(screen.queryByTestId(dropOverlayClasses.root)).not.toBeInTheDocument();
      fireEvent.dragEnter(inputEl);
      await waitFor(() => {
        expect(screen.getByTestId(dropOverlayClasses.root)).toBeInTheDocument();
        expect(screen.getByTestId(dropOverlayClasses.header).textContent).toBe('TestDropOverlayHeader');
      });
      fireEvent.dragLeave(inputEl);
      await waitFor(() => {
        expect(screen.queryByTestId(dropOverlayClasses.root)).not.toBeInTheDocument();
      });

      rerender(<FilePickerTemplate DropOverlayComponent={() => <div data-testid="custom-drop-overlay" />} />);
      inputEl = screen.getByTestId(filePickerClasses.input);
      expect(screen.queryByTestId('custom-drop-overlay')).not.toBeInTheDocument();
      fireEvent.dragEnter(inputEl);
      await waitFor(() => {
        expect(screen.getByTestId('custom-drop-overlay')).toBeInTheDocument();
      });
      fireEvent.dragLeave(inputEl);
      await waitFor(() => {
        expect(screen.queryByTestId('custom-drop-overlay')).not.toBeInTheDocument();
      });
    });

    it('should render default and custom DropZoneComponent', () => {
      const { rerender } = render(
        <FilePickerTemplate
          DropZoneComponentProps={{
            header: 'TestFilePickerBlockHeader',
          }}
        />,
      );
      expect(screen.getByTestId(filePickerBlockClasses.root)).toBeInTheDocument();
      expect(screen.getByTestId(filePickerBlockClasses.header).textContent).toBe('TestFilePickerBlockHeader');

      rerender(<FilePickerTemplate DropZoneComponent={() => <div data-testid="custom-drop-zone" />} />);
      expect(screen.getByTestId('custom-drop-zone')).toBeInTheDocument();
    });
  });

  describe('Events', () => {
    const validFile = new File([new Uint8Array(1000)], 'accept-file-ext.png', { type: 'image/png' });
    const invalidExtensionFile = new File([new Uint8Array(1000)], 'invalid-extension.txt', { type: 'text/plain' });
    const invalidSizeFile = new File([new Uint8Array(1001)], 'invalid-size.png', { type: 'image/png' });
    const files = [validFile, invalidExtensionFile, invalidSizeFile];

    const expectedOnDroppedArgs = [
      [validFile],
      [
        {
          errors: expect.arrayContaining([
            expect.objectContaining({
              code: 'file-invalid-type',
            }),
          ]),
          file: expect.objectContaining({
            name: 'invalid-extension.txt',
          }),
        },
        {
          errors: expect.arrayContaining([
            expect.objectContaining({
              code: 'file-too-large',
            }),
          ]),
          file: expect.objectContaining({
            name: 'invalid-size.png',
          }),
        },
      ],
    ];

    beforeEach(() => {
      mockOnDropped.mockClear();
    });

    it('should call onDropped after dropping', async () => {
      render(<FilePickerTemplate />);

      fireEvent.drop(screen.getByTestId(filePickerClasses.input), {
        dataTransfer: {
          files,
          types: ['Files'],
        },
      });

      await waitFor(() => {
        expect(mockOnDropped).toHaveBeenCalledWith(
          ...expectedOnDroppedArgs,
          expect.objectContaining({
            _reactName: 'onDrop',
          }),
        );
      });
    });

    it('should call onDropped after changing input', async () => {
      render(<FilePickerTemplate />);

      fireEvent.change(screen.getByTestId(filePickerClasses.input), {
        target: {
          files,
        },
      });

      await waitFor(() => {
        expect(mockOnDropped).toHaveBeenCalledWith(
          ...expectedOnDroppedArgs,
          expect.objectContaining({
            _reactName: 'onChange',
          }),
        );
      });
    });
  });
});
