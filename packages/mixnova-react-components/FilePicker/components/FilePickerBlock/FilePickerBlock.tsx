'use client';

import { Icon } from '@hxnova/icons';
import { Button } from '@hxnova/react-components/Button';
import { Tooltip } from '@hxnova/react-components/Tooltip';
import { Typography } from '@hxnova/react-components/Typography';
import clsx from 'clsx';
import { forwardRef, useMemo } from 'react';
import { useTranslate } from '../../../utils/locales';
import translations from '../../translations';
import filePickerBlockClasses from './FilePickerBlock.classes';
import { StyledDropZone, StyledFilePickerBlockContainer } from './FilePickerBlock.styled';
import { IFilePickerBlock } from './FilePickerBlock.types';

export const FilePickerBlock = forwardRef<HTMLDivElement, IFilePickerBlock>((props, ref) => {
  const t = useTranslate(translations);

  const {
    header,
    showDescription = true,
    extraDescription,
    condensed = false,
    buttonLabel = t('Upload'),
    dropZoneInstruction = t('Drag & Drop files'),
    dropZoneProps,
    FilePanelComponent = () => null,
    acceptedFilesDisplayList,
    showMaxFiles = false,
    className,
    ...restProps
  } = props;

  const acceptedFiles = acceptedFilesDisplayList?.join(', ') || dropZoneProps?.getInputProps().accept || '';
  const acceptedFilesLength = acceptedFiles.split(',').length;

  const maxFileSize = useMemo(
    () =>
      dropZoneProps?.maxSize && Number.isFinite(dropZoneProps.maxSize)
        ? parseFloat((dropZoneProps.maxSize / 1024 / 1024).toFixed(1))
        : Infinity,
    [dropZoneProps?.maxSize],
  );
  const maxFiles = dropZoneProps?.maxFiles;

  const renderHeader =
    typeof header === 'string' ? (
      <Typography
        className={filePickerBlockClasses.header}
        data-testid={filePickerBlockClasses.header}
        variant="bodySmall"
        sx={(theme) => ({
          marginBottom: theme.vars.sys.viewport.spacing.spaceBetween.vertical.xs,
        })}
      >
        {header}
      </Typography>
    ) : (
      header
    );

  const renderDropzone = (
    <StyledDropZone
      condensed={condensed}
      className={filePickerBlockClasses.dropZone}
      data-testid={filePickerBlockClasses.dropZone}
    >
      <Button
        onClick={dropZoneProps?.open}
        startIcon={<Icon family="material" name="upload" />}
        className={filePickerBlockClasses.uploadButton}
        data-testid={filePickerBlockClasses.uploadButton}
      >
        {buttonLabel}
      </Button>
      {dropZoneInstruction && (
        <div
          className={filePickerBlockClasses.instruction}
          data-testid={filePickerBlockClasses.instruction}
          sx={(theme) => ({
            marginTop: theme.vars.sys.viewport.spacing.spaceBetween.vertical,
            display: 'flex',
          })}
        >
          <Typography
            variant="bodySmall"
            className={filePickerBlockClasses.subInstruction}
            data-testid={filePickerBlockClasses.subInstruction}
          >
            {t('or')}&nbsp;
          </Typography>
          {typeof dropZoneInstruction === 'string' ? (
            <Typography variant="bodySmall">{dropZoneInstruction}</Typography>
          ) : (
            dropZoneInstruction
          )}
        </div>
      )}
    </StyledDropZone>
  );

  const renderDescription = showDescription && (
    <div
      className={filePickerBlockClasses.description}
      data-testid={filePickerBlockClasses.description}
      sx={(theme) => ({
        textAlign: 'center',
        color: theme.vars.palette.onSurfaceVariant,
        marginTop: theme.vars.sys.viewport.spacing.spaceBetween.vertical['2xs'],
      })}
    >
      {acceptedFiles && (
        <Typography
          variant="labelSmall"
          className={filePickerBlockClasses.acceptedFiles}
          data-testid={filePickerBlockClasses.acceptedFiles}
        >
          {t('Accepted file types are')}&nbsp;
          {acceptedFilesLength > 5 ? (
            <Tooltip title={acceptedFiles} placement="top" showArrow>
              <span sx={{ textDecorationLine: 'underline', cursor: 'pointer' }}>{t('here')}</span>
            </Tooltip>
          ) : (
            acceptedFiles
          )}
          .&nbsp;
        </Typography>
      )}

      {showMaxFiles && maxFiles && (
        <Typography
          variant="labelSmall"
          className={filePickerBlockClasses.maxFiles}
          data-testid={filePickerBlockClasses.maxFiles}
        >
          {t('MaximumNumberFiles', { '0': `${maxFiles}` })}.&nbsp;
        </Typography>
      )}

      {Number.isFinite(maxFileSize) && (
        <Typography
          variant="labelSmall"
          className={filePickerBlockClasses.maxSize}
          data-testid={filePickerBlockClasses.maxSize}
        >
          {t('MaximumFileSize', { '0': maxFileSize })}.
        </Typography>
      )}

      {typeof extraDescription === 'string' ? (
        <Typography
          as="p"
          variant="labelSmall"
          className={filePickerBlockClasses.extraDescription}
          data-testid={filePickerBlockClasses.extraDescription}
        >
          {extraDescription}
        </Typography>
      ) : (
        extraDescription
      )}
    </div>
  );

  return (
    <StyledFilePickerBlockContainer
      ref={ref}
      className={clsx(filePickerBlockClasses.root, className)}
      data-testid={filePickerBlockClasses.root}
      {...restProps}
    >
      {renderHeader}
      {renderDropzone}
      {renderDescription}
      <FilePanelComponent />
    </StyledFilePickerBlockContainer>
  );
});
