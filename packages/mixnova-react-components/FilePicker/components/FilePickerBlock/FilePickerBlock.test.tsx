import '@testing-library/jest-dom/vitest';
import { act, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, expect, it, vi } from 'vitest';
import { FilePanel, filePanelClasses } from '../../../FilePanel';
import { FilePickerBlock } from './FilePickerBlock';
import filePickerBlockClasses from './FilePickerBlock.classes';
import { IFilePickerBlock } from './FilePickerBlock.types';

const mockOpen = vi.fn();

const dropZoneProps = {
  maxSize: 2 * 1024 * 1024,
  maxFiles: 3,
  open: mockOpen,
  getInputProps: () => '',
} as unknown as IFilePickerBlock['dropZoneProps'];

describe('FilePickerBlock', () => {
  it('should render FilePickerBlock', () => {
    render(
      <FilePickerBlock
        buttonLabel="TestButtonLabel"
        FilePanelComponent={() => (
          <FilePanel acceptedFiles={[]} rejectedFiles={[]} onClose={() => {}} onRemove={() => {}} />
        )}
      />,
    );
    expect(screen.getByTestId(filePickerBlockClasses.root)).toBeInTheDocument();
    expect(screen.getByTestId(filePickerBlockClasses.dropZone)).toBeInTheDocument();
    expect(screen.getByTestId(filePickerBlockClasses.uploadButton)).toHaveTextContent('TestButtonLabel');
    expect(screen.getByTestId(filePanelClasses.root)).toBeInTheDocument();
  });

  it('should render props that can be string or element', () => {
    const { rerender } = render(<FilePickerBlock />);
    expect(screen.queryByTestId(filePickerBlockClasses.header)).not.toBeInTheDocument();
    expect(screen.queryByTestId(filePickerBlockClasses.extraDescription)).not.toBeInTheDocument();
    expect(screen.getByTestId(filePickerBlockClasses.subInstruction)).toHaveTextContent('or');
    expect(screen.getByTestId(filePickerBlockClasses.instruction)).toHaveTextContent('Drag & Drop files');

    rerender(
      <FilePickerBlock
        header="StringHeader"
        dropZoneInstruction="StringInstruction"
        extraDescription="StringExtraDescription"
      />,
    );
    expect(screen.getByTestId(filePickerBlockClasses.header).textContent).toBe('StringHeader');
    expect(screen.getByTestId(filePickerBlockClasses.instruction)).toHaveTextContent('StringInstruction');
    expect(screen.getByTestId(filePickerBlockClasses.extraDescription).textContent).toBe('StringExtraDescription');

    rerender(
      <FilePickerBlock
        header={<span>CustomHeader</span>}
        dropZoneInstruction={<span>CustomInstruction</span>}
        extraDescription={<span>CustomExtraDescription</span>}
      />,
    );
    expect(screen.getByText('CustomHeader')).toBeInTheDocument();
    expect(screen.getByText('CustomInstruction')).toBeInTheDocument();
    expect(screen.getByText('CustomExtraDescription')).toBeInTheDocument();
  });

  it('should conditionally render description', () => {
    const { rerender } = render(<FilePickerBlock showDescription />);
    expect(screen.getByTestId(filePickerBlockClasses.description)).toBeInTheDocument();

    rerender(<FilePickerBlock showDescription={false} />);
    expect(screen.queryByTestId(filePickerBlockClasses.description)).not.toBeInTheDocument();
  });

  it('should render acceptedFiles in Tooltip only if there are more than 5 items', async () => {
    const user = userEvent.setup();

    const { rerender } = render(<FilePickerBlock />);
    expect(screen.queryByTestId(filePickerBlockClasses.acceptedFiles)).not.toBeInTheDocument();

    const acceptedFiles = ['.jpg', '.png', '.gif', '.mp4', '.mp3'];
    rerender(<FilePickerBlock acceptedFilesDisplayList={acceptedFiles} />);
    expect(screen.getByTestId(filePickerBlockClasses.acceptedFiles)).toHaveTextContent(
      'Accepted file types are .jpg, .png, .gif, .mp4, .mp3.',
    );

    rerender(<FilePickerBlock acceptedFilesDisplayList={[...acceptedFiles, '.zip']} />);
    const expectedText = '.jpg, .png, .gif, .mp4, .mp3, .zip';
    expect(screen.getByTestId(filePickerBlockClasses.acceptedFiles)).toHaveTextContent('Accepted file types are here.');
    expect(screen.queryByText(expectedText)).not.toBeInTheDocument();
    await act(async () => {
      await user.hover(screen.getByText('here'));
    });
    expect(await screen.findByText(expectedText)).toBeInTheDocument();
  });

  it('should conditionally render maxFiles', () => {
    const { rerender } = render(<FilePickerBlock showMaxFiles />);
    expect(screen.queryByTestId(filePickerBlockClasses.maxFiles)).not.toBeInTheDocument();

    rerender(<FilePickerBlock dropZoneProps={dropZoneProps} />);
    expect(screen.queryByTestId(filePickerBlockClasses.maxFiles)).not.toBeInTheDocument();

    rerender(<FilePickerBlock showMaxFiles dropZoneProps={dropZoneProps} />);
    expect(screen.getByTestId(filePickerBlockClasses.maxFiles)).toHaveTextContent('Maximum number of files: 3.');
  });

  it('should conditionally render maxFileSize', () => {
    const { rerender } = render(<FilePickerBlock />);
    expect(screen.queryByTestId(filePickerBlockClasses.maxSize)).not.toBeInTheDocument();

    rerender(<FilePickerBlock dropZoneProps={dropZoneProps} />);
    expect(screen.getByTestId(filePickerBlockClasses.maxSize)).toHaveTextContent('Maximum file size: 2 MB.');

    rerender(
      <FilePickerBlock
        dropZoneProps={
          {
            ...dropZoneProps,
            maxSize: Infinity,
          } as IFilePickerBlock['dropZoneProps']
        }
      />,
    );
    expect(screen.queryByTestId(filePickerBlockClasses.maxSize)).not.toBeInTheDocument();
  });

  it('should call dropZoneProps.open when upload button is clicked', async () => {
    const user = userEvent.setup();

    render(<FilePickerBlock dropZoneProps={dropZoneProps} />);
    expect(mockOpen).not.toHaveBeenCalled();

    await user.click(screen.getByTestId(filePickerBlockClasses.uploadButton));
    expect(mockOpen).toHaveBeenCalled();
  });
});
