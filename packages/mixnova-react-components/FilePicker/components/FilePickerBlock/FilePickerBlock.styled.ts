import { styled } from '@pigment-css/react';
import { filePanelClasses } from '../../../FilePanel';
import { IFilePickerBlock } from './FilePickerBlock.types';

export const StyledFilePickerBlockContainer = styled('div')(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  [`& .${filePanelClasses.root}`]: {
    marginTop: theme.vars.sys.viewport.spacing.spaceBetween.vertical.lg,
  },
}));

export const StyledDropZone = styled('div', {
  shouldForwardProp: (prop) => prop !== 'condensed',
})<{
  condensed: NonNullable<IFilePickerBlock['condensed']>;
}>(({ theme }) => ({
  // TODO: replace with ${theme.vars.sys.viewport.spacing.padding.topBottom['3xl']} when updating Nova to beta version
  padding: `48px ${theme.vars.sys.viewport.spacing.padding.leftRight.lg}`,
  border: `1px dashed ${theme.vars.palette.outline}`,
  borderRadius: theme.vars.sys.viewport.radius.xs,
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  textAlign: 'center',
  gap: theme.vars.sys.viewport.spacing.spaceBetween.vertical.xs,
  transition: 'background 300ms cubic-bezier(0.2, 0.0, 0, 1.0)',
  backgroundColor: theme.vars.palette.surfaceContainer,
  '&:hover': {
    backgroundColor: `color-mix(in srgb, transparent, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
  },
  variants: [
    {
      props: {
        condensed: true,
      },
      style: {
        flexDirection: 'row',
        flex: 'initial',
        gap: theme.vars.sys.viewport.spacing.spaceBetween.horizontal.xs,
      },
    },
  ],
}));
