import generateUtilityClasses from '@mui/utils/generateUtilityClasses';

export interface FilePickerBlockClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the header. */
  header: string;
  /** Class name applied to the drop zone. */
  dropZone: string;
  /** Class name applied to the upload button. */
  uploadButton: string;
  /** Class name applied to the instruction of drop zone. */
  instruction: string;
  /** Class name applied to the sub-instruction of drop zone (the word "or"). */
  subInstruction: string;
  /** Class name applied to the description container. */
  description: string;
  /** Class name applied to the accepted file extensions description. */
  acceptedFiles: string;
  /** Class name applied to the maximum number of files description. */
  maxFiles: string;
  /** Class name applied to the maximum file size description. */
  maxSize: string;
  /** Class name applied to the extra description element below the description. */
  extraDescription: string;
}

const filePickerBlockClasses: FilePickerBlockClasses = generateUtilityClasses('MIxNovaFilePickerBlock', [
  'root',
  'dropZone',
  'header',
  'uploadButton',
  'instruction',
  'subInstruction',
  'description',
  'acceptedFiles',
  'maxFiles',
  'maxSize',
  'extraDescription',
]);

export default filePickerBlockClasses;
