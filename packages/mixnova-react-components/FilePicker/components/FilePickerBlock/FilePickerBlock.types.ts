import { FunctionComponent, HTMLAttributes, ReactNode } from 'react';
import { DropzoneOptions, DropzoneState } from 'react-dropzone';

export type IFilePickerBlock = Omit<HTMLAttributes<HTMLDivElement>, 'children'> & {
  /**
   *  If `true`, the component should render the compact horizontal style.
   *  @default false
   */
  condensed?: boolean;
  /**
   *  The header of the component. It can be a string or a custom component.
   */
  header?: ReactNode;
  /**
   *  If `true`, the description will be shown below the drop zone.
   *  @default true
   */
  showDescription?: boolean;
  /**
   *  The extra description shows below the description. It can be a string or a custom component.
   */
  extraDescription?: ReactNode;
  /**
   *  The label of the upload button.
   *  @default 'Upload'
   */
  buttonLabel?: string;
  /**
   *  The instruction of the drop zone. It can be a string or a custom component.
   *  @default 'Drag & Drop files'
   */
  dropZoneInstruction?: ReactNode;
  /**
   * The DropZone state and options.
   */
  dropZoneProps?: DropzoneState & DropzoneOptions;
  /**
   * A user-friendly list of accepted file types to display for the user.
   * If not specified, the component will display the values specified in the accept field of the dropZoneProps.
   *
   * @example ['jpg', 'jpeg', 'png']
   */
  acceptedFilesDisplayList?: ReadonlyArray<string>;
  /**
   *  If `true`, the maximum number of allowed files will be displayed in the description.
   *  @default false
   */
  showMaxFiles?: boolean;
  /**
   *  The file panel component to render below description section. Commonly used to display the list of uploaded files.
   *
   *  @default () => null
   */
  FilePanelComponent?: FunctionComponent;
};
