import generateUtilityClasses from '@mui/utils/generateUtilityClasses';

export interface DropOverlayClasses {
  /** Class name applied to the root element. */
  root: string;
  /** Class name applied to the header element. */
  header: string;
  /** Class name applied to the maximum number of files description. */
  maxFiles: string;
  /** Class name applied to the maximum file size description. */
  maxSize: string;
  /** Class name applied to the extra description element. */
  extraDescription: string;
}

const dropOverlayClasses: DropOverlayClasses = generateUtilityClasses('MIxNovaDropOverlay', [
  'root',
  'header',
  'maxFiles',
  'maxSize',
  'extraDescription',
]);

export default dropOverlayClasses;
