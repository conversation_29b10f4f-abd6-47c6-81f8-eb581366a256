'use client';

import { Icon } from '@hxnova/icons';
import { Typography } from '@hxnova/react-components/Typography';
import clsx from 'clsx';
import { forwardRef, useMemo } from 'react';
import { useTranslate } from '../../../utils/locales';
import translations from '../../translations';
import dropOverlayClasses from './DropOverlay.classes';
import { StyledIconContainer, StyledOverlay } from './DropOverlay.styled';
import { IDropOverlay } from './DropOverlay.types';

export const DropOverlay = forwardRef<HTMLDivElement, IDropOverlay>((props, ref) => {
  const t = useTranslate(translations);

  const {
    dropZoneOptions,
    header = t('Drop files anywhere to upload'),
    showMaxFiles = true,
    showMaxSize = true,
    extraDescription,
    className,
    ...restProps
  } = props;

  const maxFileSize = useMemo(
    () =>
      dropZoneOptions?.maxSize && Number.isFinite(dropZoneOptions.maxSize)
        ? parseFloat((dropZoneOptions.maxSize / 1024 / 1024).toFixed(1)) // Convert bytes to MB
        : Infinity,
    [dropZoneOptions?.maxSize],
  );
  const maxFiles = dropZoneOptions?.maxFiles;

  const renderHeader =
    typeof header === 'string' ? (
      <Typography
        variant="bodySmall"
        sx={(theme) => ({
          fontWeight: 700,
          marginBottom: theme.vars.sys.viewport.spacing.spaceBetween.vertical['2xs'],
        })}
        className={dropOverlayClasses.header}
        data-testid={dropOverlayClasses.header}
      >
        {header}
      </Typography>
    ) : (
      header
    );

  const renderMaxFiles = showMaxFiles && maxFiles && (
    <Typography variant="labelSmall" className={dropOverlayClasses.maxFiles} data-testid={dropOverlayClasses.maxFiles}>
      {t('MaximumNumberFiles', { '0': `${maxFiles}` })}.
    </Typography>
  );

  const renderMaxSize = showMaxSize && maxFileSize !== Infinity && (
    <Typography variant="labelSmall" className={dropOverlayClasses.maxSize} data-testid={dropOverlayClasses.maxSize}>
      {t('MaximumFileSize', { '0': maxFileSize })}.
    </Typography>
  );

  const renderExtraDescription =
    typeof extraDescription === 'string' ? (
      <Typography
        variant="labelSmall"
        className={dropOverlayClasses.extraDescription}
        data-testid={dropOverlayClasses.extraDescription}
      >
        {extraDescription}
      </Typography>
    ) : (
      extraDescription
    );

  return (
    <StyledOverlay
      ref={ref}
      className={clsx(dropOverlayClasses.root, className)}
      data-testid={dropOverlayClasses.root}
      {...restProps}
    >
      <StyledIconContainer>
        <Icon family="material" name="arrow_downward" />
      </StyledIconContainer>
      {renderHeader}
      {renderMaxFiles}
      {renderMaxSize}
      {renderExtraDescription}
    </StyledOverlay>
  );
});
