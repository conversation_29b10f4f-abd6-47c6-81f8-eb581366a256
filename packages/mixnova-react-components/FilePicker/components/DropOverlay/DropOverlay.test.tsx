import '@testing-library/jest-dom/vitest';
import { render, screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { DropOverlay } from './DropOverlay';
import dropOverlayClasses from './DropOverlay.classes';
import type { IDropOverlay } from './DropOverlay.types';

describe('DropOverlay', () => {
  it('should render DropOverlay', () => {
    render(<DropOverlay />);
    expect(screen.getByTestId(dropOverlayClasses.root)).toBeInTheDocument();
  });

  it('should render props that can be string or element', () => {
    const { rerender } = render(<DropOverlay />);
    expect(screen.getByTestId(dropOverlayClasses.header).textContent).toBe('Drop files anywhere to upload');
    expect(screen.queryByTestId(dropOverlayClasses.extraDescription)).not.toBeInTheDocument();

    rerender(<DropOverlay header="StringHeader" extraDescription="StringExtraDescription" />);
    expect(screen.getByTestId(dropOverlayClasses.header).textContent).toBe('StringHeader');
    expect(screen.getByTestId(dropOverlayClasses.extraDescription).textContent).toBe('StringExtraDescription');

    rerender(<DropOverlay header={<span>CustomHeader</span>} extraDescription={<span>CustomExtraDescription</span>} />);
    expect(screen.getByText('CustomHeader')).toBeInTheDocument();
    expect(screen.getByText('CustomExtraDescription')).toBeInTheDocument();
  });

  it('should conditionally render max files and max size', () => {
    const dropZoneOptions: IDropOverlay['dropZoneOptions'] = {
      maxSize: 2 * 1024 * 1024,
      maxFiles: 1,
    };

    const { rerender } = render(<DropOverlay />);
    expect(screen.queryByTestId(dropOverlayClasses.maxFiles)).not.toBeInTheDocument();
    expect(screen.queryByTestId(dropOverlayClasses.maxSize)).not.toBeInTheDocument();

    rerender(<DropOverlay dropZoneOptions={dropZoneOptions} />);
    expect(screen.getByTestId(dropOverlayClasses.maxFiles).textContent).toBe('Maximum number of files: 1.');
    expect(screen.getByTestId(dropOverlayClasses.maxSize).textContent).toBe('Maximum file size: 2 MB.');

    rerender(<DropOverlay showMaxFiles={false} showMaxSize={false} dropZoneOptions={dropZoneOptions} />);
    expect(screen.queryByTestId(dropOverlayClasses.maxFiles)).not.toBeInTheDocument();
    expect(screen.queryByTestId(dropOverlayClasses.maxSize)).not.toBeInTheDocument();

    rerender(<DropOverlay dropZoneOptions={{ maxSize: Infinity }} />);
    expect(screen.queryByTestId(dropOverlayClasses.maxSize)).not.toBeInTheDocument();
  });
});
