import { styled } from '@pigment-css/react';

export const StyledOverlay = styled('div')(({ theme }) => {
  return {
    borderRadius: theme.vars.sys.viewport.radius.xs,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    inset: 0,
    color: theme.vars.palette.surfaceContainer,
    backgroundColor: 'rgba(0, 0, 0, 0.54)',
    zIndex: 1201,
  };
});

export const StyledIconContainer = styled('div')(({ theme }) => ({
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '2.5rem',
  height: '2.5rem',
  fontSize: 24,
  backgroundColor: theme.vars.palette.secondary,
  borderRadius: '0.75rem',
  // TODO: Nova is missing theme.vars.sys.viewport.spacing.spaceBetween.vertical.sm
  marginBottom: 12,
}));
