import { HTMLAttributes, ReactNode } from 'react';
import type { DropzoneOptions } from 'react-dropzone';

export interface IDropOverlay extends Omit<HTMLAttributes<HTMLDivElement>, 'children'> {
  /**
   *  The header of the component. It could be a string or a custom component.
   *  @default 'Drop files anywhere to upload.'
   */
  header?: ReactNode;
  /**
   *  If `true`, the maximum number of files will be shown.
   *  @default true
   */
  showMaxFiles?: boolean;
  /**
   *  If `true`, the maximum file size will be shown.
   *  @default true
   */
  showMaxSize?: boolean;
  /**
   *  The extra description shows below the description. It could be a string or a custom component.
   */
  extraDescription?: ReactNode;
  /**
   * The DropZone options.
   * You can specify `maxSize` and `maxFiles` in this object to display descriptions about the allowed maximum file size and number of files.
   *
   * Refer to the [react-dropzone documentation](https://react-dropzone.js.org/#src) for more details.
   */
  dropZoneOptions?: DropzoneOptions;
}
