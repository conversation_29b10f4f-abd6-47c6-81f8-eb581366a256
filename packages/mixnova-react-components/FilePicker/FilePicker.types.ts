import { FunctionComponent, HTMLAttributes } from 'react';
import type { DropEvent, DropzoneOptions, FileRejection, FileWithPath } from 'react-dropzone';
import type { IDropOverlay } from './components/DropOverlay';
import type { IFilePickerBlock } from './components/FilePickerBlock';

export type { FileRejection, FileWithPath };

export type IFilePicker = Omit<HTMLAttributes<HTMLDivElement>, 'children'> & {
  /**
   * Options to configure the react-dropzone root element. This may be useful to define accepted file types, maximum file number and size,...
   *
   * Refer to the [react-dropzone documentation](https://react-dropzone.js.org/#src) for more details.
   */
  dropZoneOptions: DropzoneOptions;
  /**
   * Callback fired whenever a drop event occurs, regardless of whether the dropped files are accepted or rejected.
   */
  onDropped: (acceptedFiles: FileWithPath[], rejectedFiles: FileRejection[], event: DropEvent) => void;
  /**
   * The content of FilePicker component shown when no drag is active.
   *
   * By default, it uses the built-in FilePickerBlock component.
   *
   * @default FilePickerBlock
   */
  DropZoneComponent?: FunctionComponent<IFilePickerBlock>;
  /**
   * Props applied to the DropZoneComponent.
   *
   *  ```
   *  export interface IFilePickerBlock {
   *   condensed?: boolean;
   *   header?: ReactNode;
   *   showDescription?: boolean;
   *   acceptedFilesDisplayList?: string[];
   *   showMaxFiles?: boolean;
   *   extraDescription?: ReactNode;
   *   buttonLabel?: string;
   *   dragZoneInstructions?: ReactNode;
   *   dropZoneProps?: DropzoneState & DropzoneOptions;
   *   FilePanelComponent?: ComponentType<FilePanelComponentProps>;
   * }
   *  ```
   *
   */
  DropZoneComponentProps?: IFilePickerBlock;
  /**
   * The component used for drag and drop target overlay. This component is shown when the user drags files over the drop zone.
   *
   * By default, it uses the built-in DropOverlay component.
   *
   * @default DropOverlay
   */
  DropOverlayComponent?: FunctionComponent<IDropOverlay>;
  /**
   * Props applied to the DropOverlayComponent.
   *
   *  ```
   *  export interface DropOverlayProps extends StackProps {
   *   header?: ReactNode;
   *   showMaxFiles?: boolean;
   *   showMaxSize?: boolean;
   *   extraDescription?: ReactNode;
   *   dropZoneOptions?: DropzoneOptions;
   * }
   *  ```
   */
  DropOverlayComponentProps?: IDropOverlay;
};
