'use client';

import clsx from 'clsx';
import { useDropzone } from 'react-dropzone';
import { DropOverlay } from './components/DropOverlay';
import { FilePickerBlock } from './components/FilePickerBlock';
import filePickerClasses from './FilePicker.classes';
import { IFilePicker } from './FilePicker.types';

export const FilePicker = (props: IFilePicker) => {
  const {
    dropZoneOptions,
    onDropped,
    DropZoneComponent = FilePickerBlock,
    DropZoneComponentProps,
    DropOverlayComponent = DropOverlay,
    DropOverlayComponentProps,
    className,
    ...restProps
  } = props;

  const dropZoneState = useDropzone({
    ...dropZoneOptions,
    onDrop: onDropped,
  });

  const { getRootProps, getInputProps, isDragActive } = dropZoneState;

  const rootProps = getRootProps({
    className: clsx(filePickerClasses.root, className),
    'data-testid': filePickerClasses.root,
    ...restProps,
  });

  const inputProps = getInputProps({
    className: filePickerClasses.input,
    'data-testid': filePickerClasses.input,
    'aria-label': 'File picker input',
  });

  return (
    <div
      sx={{
        position: 'relative',
        height: '100%',
        overflow: 'auto',
      }}
      {...rootProps}
    >
      <input {...inputProps} />
      {isDragActive && <DropOverlayComponent dropZoneOptions={dropZoneOptions} {...DropOverlayComponentProps} />}
      <DropZoneComponent
        dropZoneProps={{ ...dropZoneState, ...dropZoneOptions }}
        // toggle visibility so that it maintains the size of the element when the overlay appears
        style={{
          visibility: isDragActive ? 'hidden' : 'visible',
        }}
        {...DropZoneComponentProps}
      />
    </div>
  );
};
