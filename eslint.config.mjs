import js from '@eslint/js';
import typeScriptPlugin from '@typescript-eslint/eslint-plugin';
import typescriptEslintParser from '@typescript-eslint/parser';
import prettierRecommended from 'eslint-plugin-prettier/recommended';
import reactPlugin from 'eslint-plugin-react';
import reactHooks from 'eslint-plugin-react-hooks';
import reactRefresh from 'eslint-plugin-react-refresh';
import reactRecommended from 'eslint-plugin-react/configs/recommended.js';
import storybookPlugin from 'eslint-plugin-storybook';
import globals from 'globals';
import tseslint from 'typescript-eslint';

const reactConfig = {
  ...reactRecommended,
  settings: {
    react: {
      version: 'detect',
      runtime: 'automatic',
    },
  },
};

export default [
  js.configs.recommended,
  ...tseslint.configs.recommended,
  reactConfig,
  prettierRecommended,
  {
    plugins: {
      'react-hooks': reactHooks,
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
    },
  },
  {
    ignores: [
      '**/**/dist',
      '**/**/coverage',
      '**/**/public',
      'pnpm-lock.yaml',
      'pnpm-workspace.yaml',
      'eslint.config.js',
      '**/**/.next',
      '.nx/**',
      '**/**/storybook-static',
    ],
  },
  {
    files: ['**/**.{ts,tsx,js,mjs,jsx}'],
    plugins: { typeScriptPlugin, reactPlugin },
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
        ...globals.es2021,
      },
      parser: typescriptEslintParser,
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
  },
  {
    files: ['apps/vite-demo/**/**.{ts,tsx,js,jsx}', 'apps/storybook/**/**.{ts,tsx,js,jsx}'],
    plugins: {
      'react-refresh': reactRefresh,
    },
    languageOptions: {
      parser: typescriptEslintParser,
    },
    rules: {
      'react-refresh/only-export-components': ['warn', { allowConstantExport: true }],
    },
  },
  {
    files: ['apps/storybook/**/**.{ts,tsx,js,jsx}'],
    plugins: { storybookPlugin },
  },
  {
    files: [
      'apps/nextjs-demo/**/**.{ts,tsx,js,jsx}',
      'apps/vite-demo/**/**.{ts,tsx,js,jsx}',
      'apps/storybook/**/**.{ts,tsx,js,jsx}',
      'packages/mixnova-react-components/**/**.{ts,tsx,js,jsx}',
    ],
    rules: {
      'react/react-in-jsx-scope': 'off',
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      'react/no-unknown-property': ['error', { ignore: ['sx'] }],
      'react/prop-types': 'off',
      'react/display-name': 'off',
      'react/jsx-key': 'warn',
    },
  },
];
